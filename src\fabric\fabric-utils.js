// Fabric.js integration utilities for AI-generated content
import { fabric } from "fabric"

// Get Fabric instance (for compatibility with different versions)
export const getFabric = async () => {
  return fabric
}

// Add AI-generated image to canvas
export const addImageToCanvas = async (canvas, imageUrl, options = {}) => {
  if (!canvas) {
    throw new Error('Canvas is required')
  }

  if (!imageUrl) {
    throw new Error('Image URL is required')
  }

  try {
    const fabricInstance = await getFabric()
    
    // Create image from URL
    const image = await fabricInstance.FabricImage.fromURL(imageUrl, {
      crossOrigin: 'anonymous'
    })

    // Set default properties
    const defaultOptions = {
      id: `ai-image-${Date.now()}`,
      padding: 10,
      cornerSize: 10,
      imageSmoothing: true,
      strokeWidth: 0,
      stroke: null,
      selectable: true,
      evented: true,
      ...options
    }

    image.set(defaultOptions)

    // Auto-scale if image is too large
    const maxDimension = options.maxDimension || 400
    if (image.width > maxDimension || image.height > maxDimension) {
      const scale = Math.min(maxDimension / image.width, maxDimension / image.height)
      image.scale(scale)
    }

    // Position image (center by default or use provided position)
    if (!options.left && !options.top) {
      const canvasCenter = canvas.getCenter()
      image.set({
        left: canvasCenter.left,
        top: canvasCenter.top,
        originX: 'center',
        originY: 'center'
      })
    }

    // Add to canvas
    canvas.add(image)
    canvas.setActiveObject(image)
    canvas.renderAll()

    return image
  } catch (error) {
    console.error("Error adding AI image to canvas:", error)
    throw new Error(`Failed to add image to canvas: ${error.message}`)
  }
}

// Replace selected object with AI-generated image
export const replaceSelectedWithImage = async (canvas, imageUrl, options = {}) => {
  if (!canvas) {
    throw new Error('Canvas is required')
  }

  const activeObject = canvas.getActiveObject()
  if (!activeObject) {
    throw new Error('No object selected')
  }

  try {
    // Get position and size of selected object
    const objectBounds = {
      left: activeObject.left,
      top: activeObject.top,
      width: activeObject.width * activeObject.scaleX,
      height: activeObject.height * activeObject.scaleY,
      angle: activeObject.angle
    }

    // Remove selected object
    canvas.remove(activeObject)

    // Add new image at the same position
    const image = await addImageToCanvas(canvas, imageUrl, {
      ...options,
      left: objectBounds.left,
      top: objectBounds.top,
      maxDimension: Math.max(objectBounds.width, objectBounds.height)
    })

    // Apply similar transformations
    if (objectBounds.angle) {
      image.set('angle', objectBounds.angle)
    }

    canvas.renderAll()
    return image
  } catch (error) {
    console.error("Error replacing object with AI image:", error)
    throw error
  }
}

// Apply AI-processed image to selected object
export const applyProcessedImageToSelected = async (canvas, processedImageUrl) => {
  if (!canvas) {
    throw new Error('Canvas is required')
  }

  const activeObject = canvas.getActiveObject()
  if (!activeObject || activeObject.type !== 'image') {
    throw new Error('Please select an image object')
  }

  try {
    const fabricInstance = await getFabric()
    
    // Load the processed image
    const newImage = await fabricInstance.FabricImage.fromURL(processedImageUrl, {
      crossOrigin: 'anonymous'
    })

    // Get current object properties
    const currentProps = {
      left: activeObject.left,
      top: activeObject.top,
      scaleX: activeObject.scaleX,
      scaleY: activeObject.scaleY,
      angle: activeObject.angle,
      opacity: activeObject.opacity,
      flipX: activeObject.flipX,
      flipY: activeObject.flipY,
      skewX: activeObject.skewX,
      skewY: activeObject.skewY
    }

    // Apply current properties to new image
    newImage.set(currentProps)
    newImage.set({
      id: activeObject.id || `processed-image-${Date.now()}`,
      padding: activeObject.padding || 10,
      cornerSize: activeObject.cornerSize || 10
    })

    // Replace the object
    canvas.remove(activeObject)
    canvas.add(newImage)
    canvas.setActiveObject(newImage)
    canvas.renderAll()

    return newImage
  } catch (error) {
    console.error("Error applying processed image:", error)
    throw new Error(`Failed to apply processed image: ${error.message}`)
  }
}

// Create image from data URL (for client-side processed images)
export const addDataUrlImageToCanvas = async (canvas, dataUrl, options = {}) => {
  return addImageToCanvas(canvas, dataUrl, options)
}

// Get image data URL from selected object
export const getSelectedImageDataUrl = (canvas, format = 'png', quality = 1.0) => {
  if (!canvas) {
    throw new Error('Canvas is required')
  }

  const activeObject = canvas.getActiveObject()
  if (!activeObject || activeObject.type !== 'image') {
    throw new Error('Please select an image object')
  }

  try {
    // Create temporary canvas for the selected object
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')
    
    // Set canvas size to object size
    const objectBounds = activeObject.getBoundingRect()
    tempCanvas.width = objectBounds.width
    tempCanvas.height = objectBounds.height
    
    // Get the image element
    const imageElement = activeObject._originalElement || activeObject._element
    if (!imageElement) {
      throw new Error('Could not access image element')
    }
    
    // Draw the image to temporary canvas
    tempCtx.drawImage(
      imageElement,
      0, 0,
      imageElement.width, imageElement.height,
      0, 0,
      tempCanvas.width, tempCanvas.height
    )
    
    // Return data URL
    return tempCanvas.toDataURL(`image/${format}`, quality)
  } catch (error) {
    console.error("Error getting image data URL:", error)
    throw new Error(`Failed to get image data: ${error.message}`)
  }
}

// Get selected image URL (if available)
export const getSelectedImageUrl = (canvas) => {
  if (!canvas) {
    throw new Error('Canvas is required')
  }

  const activeObject = canvas.getActiveObject()
  if (!activeObject || activeObject.type !== 'image') {
    throw new Error('Please select an image object')
  }

  try {
    // Try to get the original source URL
    const imageElement = activeObject._originalElement || activeObject._element
    return imageElement?.src || imageElement?.currentSrc
  } catch (error) {
    console.error("Error getting image URL:", error)
    return null
  }
}

// Batch add multiple AI images
export const addMultipleImagesToCanvas = async (canvas, imageUrls, options = {}) => {
  if (!canvas || !Array.isArray(imageUrls)) {
    throw new Error('Canvas and image URLs array are required')
  }

  const results = []
  const spacing = options.spacing || 50
  const startX = options.startX || 100
  const startY = options.startY || 100

  for (let i = 0; i < imageUrls.length; i++) {
    try {
      const imageOptions = {
        ...options,
        left: startX + (i * spacing),
        top: startY + (Math.floor(i / 5) * spacing), // Arrange in rows of 5
        maxDimension: options.maxDimension || 200
      }

      const image = await addImageToCanvas(canvas, imageUrls[i], imageOptions)
      results.push(image)
    } catch (error) {
      console.error(`Failed to add image ${i}:`, error)
      results.push(null)
    }
  }

  return results
}

// Utility to check if object is an AI-generated image
export const isAiGeneratedImage = (object) => {
  return object && 
         object.type === 'image' && 
         (object.id?.includes('ai-image') || object.id?.includes('processed-image'))
}

// Get all AI images on canvas
export const getAiImagesOnCanvas = (canvas) => {
  if (!canvas) return []
  
  return canvas.getObjects().filter(isAiGeneratedImage)
}

// Remove all AI images from canvas
export const removeAllAiImages = (canvas) => {
  if (!canvas) return
  
  const aiImages = getAiImagesOnCanvas(canvas)
  aiImages.forEach(image => canvas.remove(image))
  canvas.renderAll()
  
  return aiImages.length
}
