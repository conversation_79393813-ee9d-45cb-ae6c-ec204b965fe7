import Replicate from 'replicate'
import { fal } from '@fal-ai/client'
import Together from 'together-ai'

// Provider constants
export const AI_PROVIDERS = {
  TOGETHER: 'together',
  FAL: 'fal',
  REPLICATE: 'replicate'
}

// Model configuration
export const AI_MODELS = {
  // Together AI Models
  FLUX_SCHNELL_TOGETHER: {
    id: 'black-forest-labs/FLUX.1-schnell',
    name: 'FLUX <PERSON>hnell (Together)',
    description: 'Fast FLUX model via Together AI',
    category: 'text-to-image',
    provider: AI_PROVIDERS.TOGETHER,
    default: true
  },
  FLUX_DEV_TOGETHER: {
    id: 'black-forest-labs/FLUX.1-dev',
    name: 'FLUX Dev (Together)',
    description: 'High quality FLUX model via Together AI',
    category: 'text-to-image',
    provider: AI_PROVIDERS.TOGETHER
  },
  FLUX_KONTEXT_MAX_TOGETHER: {
    id: 'black-forest-labs/FLUX.1-Kontext-Max',
    name: 'FLUX Kontext Max (Together)',
    description: 'Context-aware FLUX model via Together AI',
    category: 'image-to-image',
    provider: AI_PROVIDERS.TOGETHER
  },
  FLUX_KONTEXT_DEV_TOGETHER: {
    id: 'black-forest-labs/FLUX.1-Kontext-Dev',
    name: 'FLUX Kontext Dev (Together)',
    description: 'Context development FLUX model via Together AI',
    category: 'image-to-image',
    provider: AI_PROVIDERS.TOGETHER
  },

  // Fal.ai Models
  FLUX_PRO_FAL: {
    id: 'fal-ai/flux-pro/v1.1',
    name: 'FLUX Pro 1.1 (Fal)',
    description: 'Premium quality FLUX model via Fal.ai',
    category: 'text-to-image',
    provider: AI_PROVIDERS.FAL
  },
  FLUX_DEV_FAL: {
    id: 'fal-ai/flux/dev',
    name: 'FLUX Dev (Fal)',
    description: 'Development FLUX model via Fal.ai',
    category: 'text-to-image',
    provider: AI_PROVIDERS.FAL
  },
  REMOVE_BG_FAL: {
    id: 'fal-ai/birefnet',
    name: 'BiRefNet (Fal)',
    description: 'Background removal via Fal.ai',
    category: 'image-processing',
    provider: AI_PROVIDERS.FAL
  },
  UPSCALE_FAL: {
    id: 'fal-ai/clarity-upscaler',
    name: 'Clarity Upscaler (Fal)',
    description: 'Image upscaling via Fal.ai',
    category: 'image-processing',
    provider: AI_PROVIDERS.FAL
  },

  // Replicate Models
  FLUX_SCHNELL_REPLICATE: {
    id: 'black-forest-labs/flux-schnell',
    name: 'FLUX Schnell (Replicate)',
    description: 'Fast FLUX model via Replicate',
    category: 'text-to-image',
    provider: AI_PROVIDERS.REPLICATE
  },
  FLUX_DEV_REPLICATE: {
    id: 'black-forest-labs/flux-dev',
    name: 'FLUX Dev (Replicate)',
    description: 'High quality FLUX model via Replicate',
    category: 'text-to-image',
    provider: AI_PROVIDERS.REPLICATE
  },
  STABLE_DIFFUSION_REPLICATE: {
    id: 'stability-ai/stable-diffusion',
    name: 'Stable Diffusion (Replicate)',
    description: 'Classic Stable Diffusion model via Replicate',
    category: 'text-to-image',
    provider: AI_PROVIDERS.REPLICATE
  },
  REMOVE_BG: {
    id: 'cjwbw/rembg',
    name: 'Background Removal (Replicate)',
    description: 'Background removal via Replicate',
    category: 'image-processing',
    provider: AI_PROVIDERS.REPLICATE
  },
  UPSCALE_REPLICATE: {
    id: 'nightmareai/real-esrgan',
    name: 'Real-ESRGAN (Replicate)',
    description: 'Image upscaling via Replicate',
    category: 'image-processing',
    provider: AI_PROVIDERS.REPLICATE
  }
}

// Initialize clients
let replicate = null
let together = null

// Initialize Replicate
try {
  replicate = new Replicate({
    auth: process.env.REPLICATE_API_TOKEN || process.env.NEXT_PUBLIC_REPLICATE_API_TOKEN,
  })
} catch (error) {
  console.warn('Failed to initialize Replicate:', error)
}

// Initialize Fal.ai
try {
  if (process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY) {
    fal.config({
      credentials: process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY,
    })
  }
} catch (error) {
  console.warn('Failed to initialize Fal.ai:', error)
}

// Initialize Together AI
try {
  together = new Together({
    apiKey: process.env.TOGETHER_API_KEY || process.env.NEXT_PUBLIC_TOGETHER_API_KEY,
  })
} catch (error) {
  console.warn('Failed to initialize Together AI:', error)
}

// Provider detection functions
export function isApiConfigured(provider = null) {
  if (provider) {
    switch (provider) {
      case AI_PROVIDERS.TOGETHER:
        const togetherToken = process.env.TOGETHER_API_KEY || process.env.NEXT_PUBLIC_TOGETHER_API_KEY
        return !!togetherToken && togetherToken !== 'your-together-api-key-here'

      case AI_PROVIDERS.FAL:
        const falToken = process.env.FAL_KEY || process.env.NEXT_PUBLIC_FAL_KEY
        return !!falToken && falToken !== 'your-fal-api-key-here'

      case AI_PROVIDERS.REPLICATE:
        const replicateToken = process.env.REPLICATE_API_TOKEN || process.env.NEXT_PUBLIC_REPLICATE_API_TOKEN
        return !!replicateToken && replicateToken !== 'your-replicate-api-token-here'
    }
  }

  return isApiConfigured(AI_PROVIDERS.TOGETHER) ||
         isApiConfigured(AI_PROVIDERS.FAL) ||
         isApiConfigured(AI_PROVIDERS.REPLICATE)
}

export function getAvailableProviders() {
  const providers = []
  
  if (isApiConfigured(AI_PROVIDERS.TOGETHER)) {
    providers.push(AI_PROVIDERS.TOGETHER)
  }
  if (isApiConfigured(AI_PROVIDERS.FAL)) {
    providers.push(AI_PROVIDERS.FAL)
  }
  if (isApiConfigured(AI_PROVIDERS.REPLICATE)) {
    providers.push(AI_PROVIDERS.REPLICATE)
  }
  
  return providers
}

export function getDefaultModel(category = 'text-to-image') {
  const availableProviders = getAvailableProviders()
  
  // Priority order: Together AI > Fal.ai > Replicate
  for (const provider of [AI_PROVIDERS.TOGETHER, AI_PROVIDERS.FAL, AI_PROVIDERS.REPLICATE]) {
    if (availableProviders.includes(provider)) {
      const models = Object.values(AI_MODELS).filter(
        model => model.provider === provider && model.category === category
      )
      
      // Return default model or first available model
      const defaultModel = models.find(model => model.default)
      return defaultModel || models[0]
    }
  }
  
  throw new Error('No AI providers configured')
}

export function getAvailableModels(category = null) {
  const availableProviders = getAvailableProviders()
  
  return Object.values(AI_MODELS).filter(model => {
    const providerAvailable = availableProviders.includes(model.provider)
    const categoryMatch = !category || model.category === category
    return providerAvailable && categoryMatch
  })
}

// Input sanitization
export function sanitizePrompt(prompt) {
  // Remove potentially harmful content
  const sanitized = prompt
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim()

  // Limit length
  return sanitized.substring(0, 1000)
}

// Retry logic with exponential backoff
async function retryWithBackoff(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      if (i === maxRetries - 1) throw error

      const delay = Math.pow(2, i) * 1000 // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
}

// Debug logging
const DEBUG_MODE = process.env.NODE_ENV === 'development'

function debugLog(message, data = null) {
  if (DEBUG_MODE) {
    console.log(`[AI Service Debug] ${message}`, data)
  }
}

// Together AI Implementation
async function generateImageTogether(modelId, options) {
  if (!together) {
    throw new Error('Together AI not initialized')
  }

  const isKontextModel = modelId.includes('kontext')

  if (isKontextModel && !options.image_url) {
    throw new Error('FLUX Kontext models require an input image')
  }

  const requestData = {
    model: modelId,
    prompt: options.prompt,
    width: options.width,
    height: options.height,
    steps: options.num_inference_steps || 28,
    n: 1,
    response_format: "url"
  }

  if (isKontextModel && options.image_url) {
    requestData.image_url = options.image_url
  }

  debugLog('Together AI request', requestData)

  const response = await together.images.create(requestData)

  if (!response.data || !response.data[0] || !response.data[0].url) {
    throw new Error('Invalid response from Together AI')
  }

  return response.data[0].url
}

// Fal.ai Implementation
async function generateImageFal(modelId, options) {
  const result = await fal.subscribe(modelId, {
    input: {
      prompt: options.prompt,
      image_size: {
        width: options.width,
        height: options.height
      },
      num_inference_steps: options.num_inference_steps || 28,
      guidance_scale: options.guidance_scale || 7.5,
      num_images: 1,
      enable_safety_checker: true
    }
  })

  debugLog('Fal.ai response', result)

  if (!result.images || !result.images[0] || !result.images[0].url) {
    throw new Error('Invalid response from Fal.ai')
  }

  return result.images[0].url
}

// Replicate Implementation
async function generateImageReplicate(modelId, options) {
  if (!replicate) {
    throw new Error('Replicate not initialized')
  }

  const input = {
    prompt: options.prompt,
    width: options.width,
    height: options.height,
    num_inference_steps: options.num_inference_steps || 28,
    guidance_scale: options.guidance_scale || 7.5,
    num_outputs: 1
  }

  debugLog('Replicate request', input)

  const output = await replicate.run(modelId, { input })

  if (!output || !output[0]) {
    throw new Error('Invalid response from Replicate')
  }

  return Array.isArray(output) ? output[0] : output
}

// Main image generation function
export async function generateImage(prompt, options = {}) {
  const sanitizedPrompt = sanitizePrompt(prompt)
  const model = getDefaultModel('text-to-image')

  const defaultOptions = {
    prompt: sanitizedPrompt,
    width: options.width || 1024,
    height: options.height || 1024,
    num_inference_steps: options.steps || 28,
    guidance_scale: options.guidance || 7.5
  }

  debugLog('Starting image generation', { prompt: sanitizedPrompt, model, options: defaultOptions })

  try {
    let result
    switch (model.provider) {
      case AI_PROVIDERS.TOGETHER:
        result = await retryWithBackoff(() => generateImageTogether(model.id, defaultOptions))
        break
      case AI_PROVIDERS.FAL:
        result = await retryWithBackoff(() => generateImageFal(model.id, defaultOptions))
        break
      case AI_PROVIDERS.REPLICATE:
        result = await retryWithBackoff(() => generateImageReplicate(model.id, defaultOptions))
        break
      default:
        throw new Error(`Unsupported provider: ${model.provider}`)
    }

    debugLog('Image generation successful', { resultUrl: result })
    return result
  } catch (error) {
    debugLog('Image generation failed', { error: error.message })
    throw new Error(`Failed to generate image: ${error.message}`)
  }
}

// Background removal functions
async function removeBackgroundFal(imageUrl) {
  const result = await fal.subscribe(AI_MODELS.REMOVE_BG_FAL.id, {
    input: { image_url: imageUrl }
  })

  debugLog('Fal.ai background removal response', result)
  return result.image?.url || result.image
}

async function removeBackgroundReplicate(imageUrl) {
  if (!replicate) {
    throw new Error('Replicate not initialized')
  }

  const output = await replicate.run(AI_MODELS.REMOVE_BG.id, {
    input: { image: imageUrl }
  })

  debugLog('Replicate background removal response', output)
  return output
}

// Main background removal function
export async function removeBackground(imageUrl, provider = null) {
  if (!imageUrl) {
    throw new Error('Image URL is required')
  }

  // Determine provider priority
  const availableProviders = getAvailableProviders()
  const targetProvider = provider || (availableProviders.includes(AI_PROVIDERS.FAL) ? AI_PROVIDERS.FAL : availableProviders[0])

  if (!availableProviders.includes(targetProvider)) {
    throw new Error(`Provider ${targetProvider} is not configured`)
  }

  debugLog('Starting background removal', { imageUrl, provider: targetProvider })

  try {
    let result
    switch (targetProvider) {
      case AI_PROVIDERS.FAL:
        result = await retryWithBackoff(() => removeBackgroundFal(imageUrl))
        break
      case AI_PROVIDERS.REPLICATE:
        result = await retryWithBackoff(() => removeBackgroundReplicate(imageUrl))
        break
      default:
        throw new Error(`Background removal not supported for provider: ${targetProvider}`)
    }

    debugLog('Background removal successful', { resultUrl: result })
    return result
  } catch (error) {
    debugLog('Background removal failed', { error: error.message })
    throw new Error(`Failed to remove background: ${error.message}`)
  }
}

// Image upscaling functions
async function upscaleImageFal(imageUrl, scale = 2) {
  const result = await fal.subscribe(AI_MODELS.UPSCALE_FAL.id, {
    input: {
      image_url: imageUrl,
      scale_factor: scale
    }
  })

  debugLog('Fal.ai upscaling response', result)
  return result.image?.url || result.image
}

async function upscaleImageReplicate(imageUrl, scale = 2) {
  if (!replicate) {
    throw new Error('Replicate not initialized')
  }

  const output = await replicate.run(AI_MODELS.UPSCALE_REPLICATE.id, {
    input: {
      image: imageUrl,
      scale: scale
    }
  })

  debugLog('Replicate upscaling response', output)
  return output
}

// Main upscaling function
export async function upscaleImage(imageUrl, scale = 2, provider = null) {
  if (!imageUrl) {
    throw new Error('Image URL is required')
  }

  if (scale < 1 || scale > 4) {
    throw new Error('Scale must be between 1 and 4')
  }

  // Determine provider priority
  const availableProviders = getAvailableProviders()
  const targetProvider = provider || (availableProviders.includes(AI_PROVIDERS.FAL) ? AI_PROVIDERS.FAL : availableProviders[0])

  if (!availableProviders.includes(targetProvider)) {
    throw new Error(`Provider ${targetProvider} is not configured`)
  }

  debugLog('Starting image upscaling', { imageUrl, scale, provider: targetProvider })

  try {
    let result
    switch (targetProvider) {
      case AI_PROVIDERS.FAL:
        result = await retryWithBackoff(() => upscaleImageFal(imageUrl, scale))
        break
      case AI_PROVIDERS.REPLICATE:
        result = await retryWithBackoff(() => upscaleImageReplicate(imageUrl, scale))
        break
      default:
        throw new Error(`Image upscaling not supported for provider: ${targetProvider}`)
    }

    debugLog('Image upscaling successful', { resultUrl: result })
    return result
  } catch (error) {
    debugLog('Image upscaling failed', { error: error.message })
    throw new Error(`Failed to upscale image: ${error.message}`)
  }
}

// Style filters
export const AI_STYLE_FILTERS = {
  ARTISTIC: {
    name: 'Artistic',
    prompt: 'artistic style, painterly, expressive brushstrokes',
    icon: '🎨'
  },
  VINTAGE: {
    name: 'Vintage',
    prompt: 'vintage style, retro, aged, nostalgic',
    icon: '📸'
  },
  CYBERPUNK: {
    name: 'Cyberpunk',
    prompt: 'cyberpunk style, neon, futuristic, digital',
    icon: '🌆'
  },
  WATERCOLOR: {
    name: 'Watercolor',
    prompt: 'watercolor painting, soft, flowing, translucent',
    icon: '🖌️'
  },
  OIL_PAINT: {
    name: 'Oil Paint',
    prompt: 'oil painting, thick brushstrokes, rich colors',
    icon: '🖼️'
  },
  SKETCH: {
    name: 'Sketch',
    prompt: 'pencil sketch, hand-drawn, artistic lines',
    icon: '✏️'
  },
  PHOTOREALISTIC: {
    name: 'Photorealistic',
    prompt: 'photorealistic, high detail, professional photography',
    icon: '📷'
  },
  CARTOON: {
    name: 'Cartoon',
    prompt: 'cartoon style, animated, colorful, stylized',
    icon: '🎭'
  }
}

// Image variations function
export async function generateImageVariations(imageUrl, prompt = '', options = {}) {
  if (!imageUrl) {
    throw new Error('Image URL is required for variations')
  }

  // Try to use Kontext models for image-to-image generation
  const kontextModels = Object.values(AI_MODELS).filter(
    model => model.id.includes('kontext') && isApiConfigured(model.provider)
  )

  if (kontextModels.length === 0) {
    throw new Error('No image-to-image models available. Please configure Together AI with Kontext models.')
  }

  const model = kontextModels[0] // Use first available Kontext model
  const enhancedPrompt = prompt || 'Create a variation of this image with improved quality and style'

  const variationOptions = {
    ...options,
    prompt: sanitizePrompt(enhancedPrompt),
    image_url: imageUrl,
    width: options.width || 1024,
    height: options.height || 1024,
    num_inference_steps: options.steps || 28,
    guidance_scale: options.guidance || 7.5
  }

  debugLog('Starting image variations', { imageUrl, prompt: enhancedPrompt, model })

  try {
    let result
    switch (model.provider) {
      case AI_PROVIDERS.TOGETHER:
        result = await retryWithBackoff(() => generateImageTogether(model.id, variationOptions))
        break
      default:
        throw new Error(`Image variations not supported for provider: ${model.provider}`)
    }

    debugLog('Image variations successful', { resultUrl: result })
    return result
  } catch (error) {
    debugLog('Image variations failed', { error: error.message })
    throw new Error(`Failed to generate image variations: ${error.message}`)
  }
}

// Apply style filter function
export async function applyStyleFilter(imageUrl, style, customPrompt = '') {
  const styleFilter = AI_STYLE_FILTERS[style]
  if (!styleFilter) {
    throw new Error(`Unknown style filter: ${style}`)
  }

  const prompt = customPrompt || `Transform this image in ${styleFilter.prompt} style`

  return await generateImageVariations(imageUrl, prompt)
}

// Utility functions
export function getStyleFilters() {
  return Object.entries(AI_STYLE_FILTERS).map(([key, filter]) => ({
    id: key,
    ...filter
  }))
}
