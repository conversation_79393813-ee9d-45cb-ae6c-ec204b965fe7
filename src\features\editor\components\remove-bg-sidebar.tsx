import Image from "next/image";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2, Sc<PERSON><PERSON> } from "lucide-react";
import { useState, useEffect } from "react";

import { usePaywall } from "@/features/subscriptions/hooks/use-paywall";

import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";

import { useRemoveBg } from "@/features/ai/api/use-remove-bg";
import { getSelectedImageUrl, applyProcessedImageToSelected } from "@/fabric/fabric-utils";
import { isApiConfigured, AI_PROVIDERS } from "@/services/aiService";
import {
  isClientSideBackgroundRemovalAvailable
} from "@/services/clientAiService";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface RemoveBgSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
};

export const RemoveBgSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: RemoveBgSidebarProps) => {
  const { shouldBlock, triggerPaywall } = usePaywall();
  const mutation = useRemoveBg();

  const [provider, setProvider] = useState<'clipdrop' | 'fal' | 'replicate' | 'client'>('clipdrop');
  const [isProcessing, setIsProcessing] = useState(false);
  const [apiStatus, setApiStatus] = useState({
    clipdrop: false,
    fal: false,
    replicate: false,
    client: false
  });

  const selectedObject = editor?.selectedObjects[0];
  const imageSrc = editor?.canvas ? getSelectedImageUrl(editor.canvas) : null;

  useEffect(() => {
    setApiStatus({
      clipdrop: isApiConfigured(AI_PROVIDERS.CLIPDROP),
      fal: isApiConfigured(AI_PROVIDERS.FAL),
      replicate: isApiConfigured(AI_PROVIDERS.REPLICATE),
      client: false // Disabled to avoid build issues
    });
  }, []);

  const onClose = () => {
    onChangeActiveTool("select");
  };

  const onClick = async () => {
    if (shouldBlock) {
      triggerPaywall();
      return;
    }

    if (!imageSrc) {
      console.error('No image source available');
      return;
    }

    setIsProcessing(true);

    try {
      // Use API-based processing with proper provider mapping
      const apiProvider = provider === 'client' ? 'clipdrop' : provider; // Fallback to clipdrop if client selected

      mutation.mutate({
        image: imageSrc,
        provider: apiProvider
      }, {
        onSuccess: async ({ data }) => {
          if (editor?.canvas && data) {
            try {
              await applyProcessedImageToSelected(editor.canvas, data);
              onChangeActiveTool("select"); // Close the sidebar after success
            } catch (error) {
              console.error('Failed to apply processed image:', error);
              // Fallback to original method
              editor?.addImage(data);
            }
          }
        },
        onError: (error) => {
          console.error('Background removal failed:', error);
        }
      });
    } catch (error) {
      console.error('Background removal failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "remove-bg" ? "visible" : "hidden",
      )}
    >
      <ToolSidebarHeader
        title="Background removal"
        description="Remove background from image using AI"
      />
      {!imageSrc && (
        <div className="flex flex-col gap-y-4 items-center justify-center flex-1">
          <AlertTriangle className="size-4 text-muted-foreground" />
          <p className="text-muted-foreground text-xs">
            Please select an image to remove its background
          </p>
        </div>
      )}
      {imageSrc && (
        <ScrollArea>
          <div className="p-4 space-y-4">
            <div className={cn(
              "relative aspect-square rounded-md overflow-hidden transition bg-muted",
              (mutation.isPending || isProcessing) && "opacity-50",
            )}>
              <Image
                src={imageSrc}
                fill
                alt="Selected Image"
                className="object-cover"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="provider">Processing Method</Label>
              <Select value={provider} onValueChange={(value: 'clipdrop' | 'fal' | 'replicate' | 'client') => setProvider(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select processing method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="clipdrop" disabled={!apiStatus.clipdrop}>
                    ClipDrop (Recommended) {!apiStatus.clipdrop && '(Not configured)'}
                  </SelectItem>
                  <SelectItem value="fal" disabled={!apiStatus.fal}>
                    Fal.ai {!apiStatus.fal && '(Not configured)'}
                  </SelectItem>
                  <SelectItem value="replicate" disabled={!apiStatus.replicate}>
                    Replicate {!apiStatus.replicate && '(Not configured)'}
                  </SelectItem>
                  <SelectItem value="client" disabled={true}>
                    Client-side (Disabled - use API providers)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              disabled={mutation.isPending || isProcessing || (provider !== 'client' && !apiStatus[provider as keyof typeof apiStatus])}
              onClick={onClick}
              className="w-full"
            >
              {(mutation.isPending || isProcessing) ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Removing Background...
                </>
              ) : (
                <>
                  <Scissors className="w-4 h-4 mr-2" />
                  Remove Background
                </>
              )}
            </Button>

            {!Object.values(apiStatus).some(Boolean) && (
              <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <AlertTriangle className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">
                  No background removal methods available. Please configure API keys or ensure WebGL2 support.
                </span>
              </div>
            )}
          </div>
        </ScrollArea>
      )}
      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
