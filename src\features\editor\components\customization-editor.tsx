"use client";

import { useEffect, useRef, useCallback, useState } from "react";
import { fabric } from "fabric";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Footer } from "@/features/editor/components/footer";
import { Toolbar } from "@/features/editor/components/toolbar";
import { ActiveTool } from "@/features/editor/types";
import { EditableLayer } from "@/types/template";
import debounce from "lodash.debounce";
import { Loader2 } from "lucide-react";

interface CustomizationEditorProps {
  templateData: {
    id: string;
    name: string;
    width: number;
    height: number;
    json: string;
    editableLayers: EditableLayer[];
  };
  customizations: Record<string, string>;
  onCustomizationChange: (layerId: string, value: string) => void;
  onPreviewGenerated: (dataUrl: string) => void;
  activeLayerId?: string | null;
  onLayerActivation?: (layerId: string | null) => void;
}

export const CustomizationEditor = ({
  templateData,
  customizations,
  onCustomizationChange,
  onPreviewGenerated,
  activeLayerId: externalActiveLayerId,
  onLayerActivation,
}: CustomizationEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isCanvasLoading, setIsCanvasLoading] = useState(true);
  const [activeTool, setActiveTool] = useState<ActiveTool>("select");
  const isApplyingCustomizations = useRef(false);
  const [isRemovingBackground, setIsRemovingBackground] = useState(false);

  // Helper function to create a unique identifier for image objects
  const createImageUniqueId = (obj: any) => {
    const left = Math.round(obj.left || 0);
    const top = Math.round(obj.top || 0);
    const width = Math.round((obj.width || 0) * (obj.scaleX || 1));
    const height = Math.round((obj.height || 0) * (obj.scaleY || 1));
    return `${left}_${top}_${width}_${height}`;
  };

  // Helper function to check if two images match by properties
  const doImagesMatch = (obj1: any, obj2Props: any, tolerance: number = 2) => {
    const positionMatch = Math.abs(obj1.left - obj2Props.left) < tolerance &&
                         Math.abs(obj1.top - obj2Props.top) < tolerance;
    const sizeMatch = Math.abs((obj1.width * obj1.scaleX) - (obj2Props.width * obj2Props.scaleX)) < tolerance &&
                     Math.abs((obj1.height * obj1.scaleY) - (obj2Props.height * obj2Props.scaleY)) < tolerance;
    return positionMatch && sizeMatch;
  };

  // Debug: Log when component mounts
  useEffect(() => {
    console.log('CustomizationEditor mounted with templateData:', {
      width: templateData.width,
      height: templateData.height,
      editableLayers: templateData.editableLayers.length,
      hasJson: !!templateData.json,
      jsonLength: templateData.json?.length || 0
    });

    // Try to parse and log JSON structure
    if (templateData.json) {
      try {
        const parsedJson = JSON.parse(templateData.json);
        console.log('Template JSON structure:', {
          version: parsedJson.version,
          objectCount: parsedJson.objects?.length || 0,
          objects: parsedJson.objects?.map((obj: any) => ({
            type: obj.type,
            id: obj.id,
            name: obj.name
          })) || []
        });
      } catch (error) {
        console.error('Error parsing template JSON:', error);
      }
    }
  }, []);

  // Initialize editor first
  const { init, editor } = useEditor({
    defaultState: templateData.json,
    defaultWidth: templateData.width,
    defaultHeight: templateData.height,
    clearSelectionCallback: () => {
      onLayerActivation?.(null);
    },
    saveCallback: () => {
      // Generate preview when canvas changes
      generatePreview();
    },
  });

  // Debug: Log editor state
  useEffect(() => {
    if (editor) {
      console.log('Editor initialized:', {
        hasCanvas: !!editor.canvas,
        hasAutoZoom: !!editor.autoZoom,
        canvasObjects: editor.canvas?.getObjects().length || 0
      });
    }
  }, [editor]);

  // Helper method to send image to remove background API (defined first)
  const sendToRemoveBackgroundAPI = useCallback(async (imageDataUrl: string, originalObject: any) => {
    try {
      console.log('Sending image to remove background API...');

      // Call the remove background API
      const response = await fetch('/api/ai/remove-bg', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: imageDataUrl,
          provider: 'clipdrop' // Default provider
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.data && editor?.canvas) {
        console.log('Processing successful result, creating new image...');

        // Create a new image with the processed result
        try {
          // Add a flag to prevent multiple executions
          let isProcessing = false;

          // Add a timeout to prevent hanging
          const timeoutId = setTimeout(() => {
            console.error('Image loading timeout');
            setIsRemovingBackground(false);
            setActiveTool("select");
            alert('Image processing timed out. Please try again.');
          }, 10000); // 10 second timeout

          fabric.Image.fromURL(result.data, (img: fabric.Image) => {
            clearTimeout(timeoutId); // Clear timeout on success

            // Prevent multiple executions
            if (isProcessing) {
              console.log('Already processing, skipping duplicate callback');
              return;
            }
            isProcessing = true;

            console.log('fabric.Image.fromURL callback called:', { img: !!img });

            if (!img) {
              console.error('No image object created');
              alert('Failed to create processed image');
              setIsRemovingBackground(false);
              setActiveTool("select");
              return;
            }

            if (!editor?.canvas) {
              console.error('Canvas not available');
              alert('Canvas not available for image replacement');
              setIsRemovingBackground(false);
              setActiveTool("select");
              return;
            }

            try {
              console.log('Original image object:', originalObject);
              console.log('New image loaded:', img);

              // Get the current canvas objects to find the correct index
              const canvasObjects = editor.canvas.getObjects();
              const objectIndex = canvasObjects.findIndex(obj => obj === originalObject || (obj as any).id === (originalObject as any).id);

              console.log('Canvas objects:', canvasObjects.length);
              console.log('Looking for object with ID:', (originalObject as any).id);
              console.log('Found object at index:', objectIndex);

              if (objectIndex === -1) {
                console.error('Could not find original object in canvas');
                setIsRemovingBackground(false);
                setActiveTool("select");
                return;
              }

              // Preserve original properties
              const originalProps = {
                left: originalObject.left || 0,
                top: originalObject.top || 0,
                width: originalObject.width || 100,
                height: originalObject.height || 100,
                scaleX: originalObject.scaleX || 1,
                scaleY: originalObject.scaleY || 1,
                angle: originalObject.angle || 0,
                originX: originalObject.originX || 'left',
                originY: originalObject.originY || 'top',
                flipX: originalObject.flipX || false,
                flipY: originalObject.flipY || false,
                opacity: originalObject.opacity || 1,
                visible: originalObject.visible !== false,
                selectable: originalObject.selectable !== false,
                evented: originalObject.evented !== false,
                id: (originalObject as any).id
              };

              console.log('Preserving original properties:', originalProps);

              // Calculate the display dimensions
              const originalDisplayWidth = originalProps.width * originalProps.scaleX;
              const originalDisplayHeight = originalProps.height * originalProps.scaleY;

              // Calculate new scale to maintain the same display size
              const newImageWidth = img.width || 1;
              const newImageHeight = img.height || 1;
              const scaleToFitX = originalDisplayWidth / newImageWidth;
              const scaleToFitY = originalDisplayHeight / newImageHeight;

              console.log('Image scaling calculation:', {
                originalDisplayWidth,
                originalDisplayHeight,
                newImageWidth,
                newImageHeight,
                scaleToFitX,
                scaleToFitY
              });

              // Use uniform scaling to maintain aspect ratio
              const uniformScale = Math.min(scaleToFitX, scaleToFitY);

              // Calculate the actual scaled dimensions
              const scaledWidth = newImageWidth * uniformScale;
              const scaledHeight = newImageHeight * uniformScale;

              // Adjust position to center the image if needed
              const leftAdjustment = (originalDisplayWidth - scaledWidth) / 2;
              const topAdjustment = (originalDisplayHeight - scaledHeight) / 2;

              const adjustedLeft = originalProps.left + leftAdjustment;
              const adjustedTop = originalProps.top + topAdjustment;

              console.log('Position adjustment:', {
                originalLeft: originalProps.left,
                originalTop: originalProps.top,
                adjustedLeft,
                adjustedTop,
                scaledWidth,
                scaledHeight,
                leftAdjustment,
                topAdjustment
              });

              // Apply all properties to the new image
              img.set({
                left: adjustedLeft,
                top: adjustedTop,
                scaleX: uniformScale,
                scaleY: uniformScale,
                angle: originalProps.angle,
                originX: originalProps.originX,
                originY: originalProps.originY,
                flipX: originalProps.flipX,
                flipY: originalProps.flipY,
                opacity: originalProps.opacity,
                visible: originalProps.visible,
                selectable: originalProps.selectable,
                evented: originalProps.evented,
                id: originalProps.id
              });

              // Replace the object directly
              console.log('Replacing image at index', objectIndex);
              editor.canvas.remove(originalObject);
              editor.canvas.add(img);

              // Set as active object and render
              editor.canvas.setActiveObject(img);
              editor.canvas.renderAll();

              console.log('Image replaced for layer', originalProps.id, 'at index', objectIndex, img);

              // Update the customization state with the processed image
              if (originalProps.id && onCustomizationChange) {
                console.log('Updating customization state with processed image for layer:', originalProps.id);
                onCustomizationChange(originalProps.id, result.data);
              }

              // Reset state
              setIsRemovingBackground(false);
              setActiveTool("select");

            } catch (replacementError) {
              console.error('Error during image replacement:', replacementError);
              const errorMessage = replacementError instanceof Error ? replacementError.message : 'Unknown error';
              alert(`Failed to replace image: ${errorMessage}`);
              setIsRemovingBackground(false);
              setActiveTool("select");
            }
          }, {
            // Add crossOrigin to handle data URLs properly
            crossOrigin: 'anonymous'
          });
        } catch (fabricError) {
          console.error('Error calling fabric.Image.fromURL:', fabricError);
          const errorMessage = fabricError instanceof Error ? fabricError.message : 'Unknown error';
          alert(`Failed to process image: ${errorMessage}`);
          setIsRemovingBackground(false);
          setActiveTool("select");
        }
      } else {
        console.error('No result data or canvas not available');
      }
    } catch (error) {
      console.error('API call failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`Background removal failed: ${errorMessage}`);
    } finally {
      setIsRemovingBackground(false);
      setActiveTool("select");
    }
  }, [editor]);

  // Handle remove background functionality (defined after helper method)
  const handleRemoveBackground = useCallback(async () => {
    console.log('handleRemoveBackground called');
    if (!editor?.canvas || isRemovingBackground) return;

    const selectedObject = editor.canvas.getActiveObject();
    console.log('Selected object:', selectedObject);
    if (!selectedObject || selectedObject.type !== 'image') {
      alert('Please select an image to remove its background');
      return;
    }

    console.log('Starting background removal process');
    setIsRemovingBackground(true);

    try {
      // Always convert the image to base64 to avoid blob URL issues
      console.log('Converting selected image to base64...');

      // Get the image element from the selected object
      let imageElement = (selectedObject as any)._originalElement || (selectedObject as any)._element;

      if (!imageElement) {
        // Try to get the image from the src property
        const imageUrl = (selectedObject as any).src;
        if (imageUrl) {
          console.log('Loading image from URL:', imageUrl);
          const img = new Image();
          img.crossOrigin = 'anonymous';

          img.onload = () => {
            console.log('Image loaded successfully');
            // Create canvas to convert to base64
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
              throw new Error('Could not create canvas context');
            }

            canvas.width = img.naturalWidth || img.width;
            canvas.height = img.naturalHeight || img.height;
            ctx.drawImage(img, 0, 0);

            const imageDataUrl = canvas.toDataURL('image/png');
            console.log('Generated data URL length:', imageDataUrl.length);

            // Now send to API
            sendToRemoveBackgroundAPI(imageDataUrl, selectedObject);
          };

          img.onerror = (error) => {
            console.error('Failed to load image:', error);
            alert('Failed to load image for processing');
            setIsRemovingBackground(false);
            setActiveTool("select");
          };

          img.src = imageUrl;
          return; // Exit here since the actual API call is in the callback
        } else {
          throw new Error('Could not get image element or URL');
        }
      }

      // If we have an image element, convert it directly
      console.log('Converting image element to base64...');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Could not create canvas context');
      }

      canvas.width = imageElement.naturalWidth || imageElement.width;
      canvas.height = imageElement.naturalHeight || imageElement.height;
      ctx.drawImage(imageElement, 0, 0);

      const imageDataUrl = canvas.toDataURL('image/png');
      console.log('Generated data URL length:', imageDataUrl.length);

      // Now send to API
      sendToRemoveBackgroundAPI(imageDataUrl, selectedObject);

      return; // Exit here since the actual API call is in the callback
    } catch (error) {
      console.error('Background removal failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`Background removal failed: ${errorMessage}`);
      setIsRemovingBackground(false);
      setActiveTool("select");
    }
  }, [editor, isRemovingBackground, sendToRemoveBackgroundAPI]);

  // Handle tool changes (defined after editor initialization)
  const handleToolChange = useCallback((tool: ActiveTool) => {
    if (tool === "remove-bg") {
      handleRemoveBackground();
    } else {
      setActiveTool(tool);
    }
  }, [handleRemoveBackground]);

  // Generate preview from canvas (defined after editor)
  const generatePreview = useCallback(() => {
    if (!editor?.canvas) return;

    try {
      const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
      if (!workspace) return;

      const dataUrl = editor.canvas.toDataURL({
        format: 'png',
        quality: 0.9,
        multiplier: 0.5,
      });
      onPreviewGenerated(dataUrl);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  }, [editor, onPreviewGenerated]);

  // Generate high-quality download from canvas
  const generateDownload = useCallback((filename: string, quality: number = 1.0, format: string = 'png') => {
    if (!editor?.canvas) {
      console.error('No canvas available for download');
      return;
    }

    try {
      console.log('Generating high-quality download...', {
        canvasObjects: editor.canvas.getObjects().length,
        canvasSize: { width: editor.canvas.width, height: editor.canvas.height }
      });

      // Save current viewport transform
      const currentTransform = editor.canvas.viewportTransform?.slice();

      // Reset viewport transform to get the full canvas
      editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

      // Find the workspace (clip) object to get proper dimensions
      const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
      console.log('Workspace found:', !!workspace, workspace ? {
        left: workspace.left,
        top: workspace.top,
        width: workspace.width,
        height: workspace.height
      } : 'none');

      let dataUrl: string;

      if (workspace) {
        // Get the workspace bounding rectangle which accounts for position and scale
        const workspaceBounds = workspace.getBoundingRect();

        console.log('Workspace bounds (generateDownload):', {
          left: workspaceBounds.left,
          top: workspaceBounds.top,
          width: workspaceBounds.width,
          height: workspaceBounds.height
        });

        // Generate high-quality image with exact workspace bounds
        dataUrl = editor.canvas.toDataURL({
          format: format,
          quality: quality,
          multiplier: 2, // Higher resolution for download
          left: workspaceBounds.left,
          top: workspaceBounds.top,
          width: workspaceBounds.width,
          height: workspaceBounds.height,
        });
      } else {
        // Fallback to full canvas
        console.log('Using full canvas for download');
        dataUrl = editor.canvas.toDataURL({
          format: format,
          quality: quality,
          multiplier: 2,
        });
      }

      // Restore viewport transform
      if (currentTransform) {
        editor.canvas.setViewportTransform(currentTransform);
      }

      console.log('Generated dataURL length:', dataUrl.length);

      // Trigger download
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Download triggered successfully for file:', filename);
    } catch (error) {
      console.error('Failed to generate download:', error);
    }
  }, [editor]);

  // Expose download function globally for debugging (after it's defined)
  useEffect(() => {
    if (editor) {
      (window as any).debugDownload = () => {
        // Dispatch the same event as the download button
        const event = new CustomEvent('downloadCustomized', {
          detail: {
            filename: 'debug-download.png',
            quality: 1.0,
            format: 'png'
          }
        });
        window.dispatchEvent(event);
      };
    }
  }, [editor]);

  // Initialize canvas (same as main editor)
  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 10;

    const initializeCanvas = () => {
      if (!containerRef.current || !canvasRef.current) {
        console.log('Container or canvas ref not available');
        return false;
      }

      // Check if canvas element is still in the DOM
      if (!document.contains(canvasRef.current)) {
        console.log('Canvas element not in DOM, retrying...');
        return false;
      }

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      console.log(`Canvas init attempt ${retryCount + 1}/${maxRetries}:`, { containerWidth, containerHeight });

      if (containerWidth === 0 || containerHeight === 0) {
        console.log('Container has zero dimensions, retrying...');
        return false;
      }

      // Ensure minimum dimensions
      const finalWidth = Math.max(containerWidth, 300);
      const finalHeight = Math.max(containerHeight, 200);

      try {
        const canvas = new fabric.Canvas(canvasRef.current, {
          controlsAboveOverlay: true,
          preserveObjectStacking: true,
          width: finalWidth,
          height: finalHeight,
        });

        console.log('Canvas initialized with dimensions:', canvas.getWidth(), 'x', canvas.getHeight());

        init({
          initialCanvas: canvas,
          initialContainer: container,
        });

        return canvas;
      } catch (error) {
        console.error('Error initializing canvas:', error);
        return false;
      }
    };

    const attemptInit = () => {
      const canvas = initializeCanvas();

      if (!canvas && retryCount < maxRetries) {
        retryCount++;
        const delay = Math.min(100 * retryCount, 1000); // Progressive delay up to 1 second
        console.log(`Canvas init failed, retrying in ${delay}ms...`);
        setTimeout(attemptInit, delay);
      } else if (!canvas) {
        console.error('Failed to initialize canvas after maximum retries');
        setIsCanvasLoading(false);
      }

      return canvas;
    };

    // Start initialization
    const canvas = attemptInit();

    return () => {
      if (canvas) {
        try {
          canvas.dispose();
        } catch (error) {
          console.warn('Error disposing canvas:', error);
        }
      }
    };
  }, [init]);

  // Handle canvas dimension changes (separate from autoZoom to prevent loops)
  useEffect(() => {
    if (!containerRef.current || !editor?.canvas) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0 && editor?.canvas) {
          // Only set dimensions, don't call autoZoom here to prevent loops
          editor.canvas.setDimensions({ width, height });
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [editor]);

  // Auto-zoom when editor is ready and has content (only once)
  useEffect(() => {
    if (!editor?.canvas || !editor.autoZoom || !isCanvasLoading) return;

    console.log('Editor is ready, triggering auto-zoom');

    // Wait for canvas to be fully loaded with content
    const checkAndAutoZoom = () => {
      const objects = editor.canvas.getObjects();
      console.log(`Canvas has ${objects.length} objects, checking for content...`);

      if (objects.length > 1) { // More than just the workspace
        console.log('Canvas has content, triggering auto-zoom');
        editor.autoZoom();
        setIsCanvasLoading(false);
      } else {
        // If no objects yet, wait a bit more (max 5 seconds)
        const maxRetries = 25; // 25 * 200ms = 5 seconds
        const currentRetry = (checkAndAutoZoom as any).retryCount || 0;

        if (currentRetry < maxRetries) {
          (checkAndAutoZoom as any).retryCount = currentRetry + 1;
          setTimeout(checkAndAutoZoom, 200);
        } else {
          console.warn('Template loading timeout - no content found after 5 seconds');
          setIsCanvasLoading(false);
        }
      }
    };

    // Small delay to ensure canvas is ready
    setTimeout(checkAndAutoZoom, 300);
  }, [editor, isCanvasLoading]);

  // Fallback timeout to clear loading state
  useEffect(() => {
    const fallbackTimeout = setTimeout(() => {
      if (isCanvasLoading) {
        console.warn('Fallback: Clearing loading state after 10 seconds');
        setIsCanvasLoading(false);
      }
    }, 10000); // 10 seconds fallback

    return () => clearTimeout(fallbackTimeout);
  }, [isCanvasLoading]);

  // Handle selection changes to notify parent
  useEffect(() => {
    if (!editor?.canvas) return;

    const handleSelectionCreated = (e: fabric.IEvent) => {
      const target = e.target;
      if (target) {
        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);

        // Only allow selection of editable layers
        if (layer) {
          onLayerActivation?.(layerId);
        } else {
          // If a non-editable object was somehow selected, clear the selection
          console.log('Preventing selection of non-editable object:', target.type, layerId);
          editor.canvas.discardActiveObject();
          editor.canvas.renderAll();
        }
      }
    };

    const handleSelectionCleared = () => {
      onLayerActivation?.(null);
    };

    const handleTextChanged = (e: fabric.IEvent) => {
      const target = e.target;
      if (target && target.type === 'textbox') {
        // Skip if this update is coming from the sidebar to prevent circular updates
        if ((target as any)._isUpdatingFromSidebar) {
          return;
        }

        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);
        if (layer && layer.type === 'text') {
          const currentText = (target as fabric.Textbox).text || '';
          onCustomizationChange(layerId, currentText);
        }
      }
    };

    const handleMouseDown = (e: fabric.IEvent) => {
      const target = e.target;
      if (target) {
        const layerId = (target as any).id;
        const isEditable = templateData.editableLayers.some(l => l.id === layerId);
        const isWorkspace = (target as any).name === 'clip';

        // Prevent interaction with non-editable objects
        if (!isEditable && !isWorkspace) {
          console.log('Preventing interaction with locked object:', target.type, layerId);
          editor.canvas.discardActiveObject();
          editor.canvas.renderAll();
          if (e.e) {
            e.e.preventDefault();
            e.e.stopPropagation();
          }
          return false;
        }
      }
    };

    editor.canvas.on('selection:created', handleSelectionCreated);
    editor.canvas.on('selection:updated', handleSelectionCreated);
    editor.canvas.on('selection:cleared', handleSelectionCleared);
    editor.canvas.on('text:changed', handleTextChanged);
    editor.canvas.on('mouse:down', handleMouseDown);

    return () => {
      editor.canvas.off('selection:created', handleSelectionCreated);
      editor.canvas.off('selection:updated', handleSelectionCreated);
      editor.canvas.off('selection:cleared', handleSelectionCleared);
      editor.canvas.off('text:changed', handleTextChanged);
      editor.canvas.off('mouse:down', handleMouseDown);
    };
  }, [editor, templateData.editableLayers, onLayerActivation, onCustomizationChange]);

  // Debounced function to apply customizations
  const applyCustomizations = useCallback(
    debounce(() => {
      if (!editor?.canvas || isApplyingCustomizations.current) return;

      isApplyingCustomizations.current = true;
      console.log('Applying customizations:', customizations);
      console.log('Template editable layers:', templateData.editableLayers.map(l => ({ id: l.id, type: l.type, originalValue: l.originalValue })));

      templateData.editableLayers.forEach(layer => {
      const customValue = customizations[layer.id];
      console.log(`Processing layer ${layer.id} (${layer.type}):`, customValue);

      // Skip if no custom value or if it's the same as original value
      if (!customValue || customValue === layer.originalValue) {
        console.log(`Skipping layer ${layer.id} - no custom value or same as original`);
        return;
      }

      // First try to find by ID
      let canvasObject = editor.canvas.getObjects().find((obj: any) => obj.id === layer.id);

      // If not found by ID, try to find using the same matching logic as during initialization
      if (!canvasObject && layer.type === 'image' && layer.originalValue) {
        console.log(`Canvas object not found by ID for layer ${layer.id}, trying property matching...`);

        try {
          const originalProps = JSON.parse(layer.originalValue);
          const allObjects = editor.canvas.getObjects();
          const imageObjects = allObjects.filter((obj: any) => obj.type === 'image');

          // Try exact position and size match
          canvasObject = imageObjects.find((obj: any) => doImagesMatch(obj, originalProps, 2));

          if (canvasObject) {
            console.log(`Found canvas object by property matching for layer ${layer.id}`);
            // Assign the ID for future lookups
            (canvasObject as any).id = layer.id;
          }
        } catch (e) {
          console.warn(`Failed to parse originalValue for canvas object lookup:`, e);
        }
      }

      if (!canvasObject) {
        console.log(`Canvas object not found for layer ${layer.id}`);
        return;
      }

      if (layer.type === 'text' && canvasObject.type === 'textbox') {
        const textbox = canvasObject as fabric.Textbox;
        console.log(`Current text: "${textbox.text}", New text: "${customValue}"`);

        if (customValue !== undefined && textbox.text !== customValue) {
          console.log(`Updating text for ${layer.id} to: "${customValue}"`);
          // Temporarily disable event handlers to prevent circular updates
          const isUpdatingFromSidebar = true;
          (textbox as any)._isUpdatingFromSidebar = isUpdatingFromSidebar;

          textbox.set('text', customValue);
          if (editor?.canvas) {
            editor.canvas.renderAll();
          }

          // Clean up the flag after a short delay
          setTimeout(() => {
            delete (textbox as any)._isUpdatingFromSidebar;
          }, 100);
        }
      } else if (layer.type === 'image' && customValue) {
        console.log(`Replacing image for ${layer.id} with: ${customValue}`);
        console.log(`Custom value type: ${typeof customValue}, starts with {: ${customValue.startsWith('{')}`);

        // Check if customValue is the original JSON properties - if so, skip replacement
        if (customValue === layer.originalValue) {
          console.log(`Skipping image replacement for ${layer.id} - customValue is same as originalValue`);
          return;
        }

        // Extract the image URL from the customValue
        let imageUrl = customValue;

        // If customValue is a JSON string (from stored properties), extract the src
        if (typeof customValue === 'string' && customValue.startsWith('{')) {
          try {
            const imageProps = JSON.parse(customValue);
            if (imageProps.src) {
              imageUrl = imageProps.src;
              console.log(`Extracted image URL from JSON: ${imageUrl}`);
            } else {
              console.warn('No src property found in image JSON, skipping replacement');
              return;
            }
          } catch (e) {
            console.warn('Failed to parse image properties JSON, treating as direct URL:', e);
            // If it's not valid JSON but starts with {, it's probably malformed - skip it
            if (customValue.startsWith('{')) {
              console.warn('Malformed JSON detected, skipping image replacement');
              return;
            }
          }
        }

        // Validate that we have a proper URL
        if (!imageUrl || !imageUrl.trim() || (!imageUrl.startsWith('http') && !imageUrl.startsWith('blob:'))) {
          console.warn(`Invalid image URL detected: ${imageUrl}, skipping replacement`);
          return;
        }

        // Check if this image is already being processed to prevent infinite loops
        const currentSrc = (canvasObject as fabric.Image).getSrc?.();
        if (currentSrc === imageUrl) {
          console.log(`Image ${layer.id} already has the correct source, skipping replacement`);
          return;
        }

        // Additional check: if imageUrl is empty or invalid, skip
        if (!imageUrl || imageUrl.trim() === '') {
          console.log(`Image ${layer.id} has empty URL, skipping replacement`);
          return;
        }

        // Handle image replacement
        fabric.Image.fromURL(imageUrl, (img) => {
          if (!editor.canvas) {
            console.error('Canvas not available during image replacement');
            return;
          }

          if (!img) {
            console.error('Failed to load image from URL:', customValue);
            return;
          }

          console.log('Original image object:', canvasObject);
          console.log('New image loaded:', img);

          // Get ALL the original object's properties to preserve exact positioning
          const originalProps = {
            left: canvasObject.left,
            top: canvasObject.top,
            width: canvasObject.width,
            height: canvasObject.height,
            scaleX: canvasObject.scaleX,
            scaleY: canvasObject.scaleY,
            angle: canvasObject.angle,
            originX: canvasObject.originX,
            originY: canvasObject.originY,
            flipX: canvasObject.flipX,
            flipY: canvasObject.flipY,
            opacity: canvasObject.opacity,
            visible: canvasObject.visible,
            shadow: canvasObject.shadow,
            stroke: canvasObject.stroke,
            strokeWidth: canvasObject.strokeWidth,
            fill: canvasObject.fill,
            selectable: canvasObject.selectable,
            evented: canvasObject.evented,
            hasControls: canvasObject.hasControls,
            hasBorders: canvasObject.hasBorders,
            lockMovementX: canvasObject.lockMovementX,
            lockMovementY: canvasObject.lockMovementY,
            lockRotation: canvasObject.lockRotation,
            lockScalingX: canvasObject.lockScalingX,
            lockScalingY: canvasObject.lockScalingY,
            lockUniScaling: canvasObject.lockUniScaling,
          };

          console.log('Preserving original properties:', originalProps);

          // Calculate the display dimensions of the original image
          const originalDisplayWidth = (originalProps.width || 100) * (originalProps.scaleX || 1);
          const originalDisplayHeight = (originalProps.height || 100) * (originalProps.scaleY || 1);

          // Ensure we have valid dimensions
          if (originalDisplayWidth <= 0 || originalDisplayHeight <= 0 || !img.width || !img.height) {
            console.error('Invalid dimensions detected:', {
              originalDisplayWidth,
              originalDisplayHeight,
              newImageWidth: img.width,
              newImageHeight: img.height
            });
            return;
          }

          // Calculate scale factors to fit the new image into the same display area
          // We want to maintain the aspect ratio and fit within the original boundaries
          const scaleToFitX = originalDisplayWidth / img.width!;
          const scaleToFitY = originalDisplayHeight / img.height!;

          // Use the smaller scale factor to ensure the image fits within the boundaries
          // This maintains aspect ratio and prevents overflow
          const uniformScale = Math.min(scaleToFitX, scaleToFitY);

          console.log('Image scaling calculation:', {
            originalDisplayWidth,
            originalDisplayHeight,
            newImageWidth: img.width,
            newImageHeight: img.height,
            scaleToFitX,
            scaleToFitY,
            uniformScale
          });

          // Calculate the actual dimensions after scaling
          const scaledWidth = img.width! * uniformScale;
          const scaledHeight = img.height! * uniformScale;

          // If the scaled image is smaller than the original area, center it
          let adjustedLeft = originalProps.left;
          let adjustedTop = originalProps.top;

          if (scaledWidth < originalDisplayWidth) {
            const widthDiff = originalDisplayWidth - scaledWidth;
            adjustedLeft = (originalProps.left || 0) + (widthDiff / 2);
          }

          if (scaledHeight < originalDisplayHeight) {
            const heightDiff = originalDisplayHeight - scaledHeight;
            adjustedTop = (originalProps.top || 0) + (heightDiff / 2);
          }

          console.log('Position adjustment:', {
            originalLeft: originalProps.left,
            originalTop: originalProps.top,
            adjustedLeft,
            adjustedTop,
            scaledWidth,
            scaledHeight,
            originalDisplayWidth,
            originalDisplayHeight
          });

          // Apply all original properties to the new image, with adjusted positioning and uniform scaling
          img.set({
            ...originalProps,
            left: adjustedLeft,
            top: adjustedTop,
            scaleX: uniformScale,
            scaleY: uniformScale,
          });

          // Ensure the image is positioned exactly as calculated
          img.setCoords();

          // Set the ID using a custom property
          (img as any).id = layer.id;

          // Get the exact position in the layer stack
          const objectIndex = editor.canvas.getObjects().indexOf(canvasObject);
          console.log(`Replacing image at index ${objectIndex}`);

          // Only proceed if we found the object
          if (objectIndex === -1) {
            console.warn(`Cannot find object ${layer.id} in canvas objects`);
            return;
          }

          // Remove old image and insert new one at exact same position
          editor.canvas.remove(canvasObject);
          editor.canvas.insertAt(img, objectIndex, false);

          // Ensure the image is properly initialized before rendering
          try {
            // Update coordinates first
            img.setCoords();

            // Render without controls first
            if (editor?.canvas) {
              editor.canvas.renderAll();
            }

            // Small delay before selecting to avoid the getRetinaScaling error
            setTimeout(() => {
              try {
                // Select the new image to show it's been replaced
                if (editor?.canvas && editor.canvas.getContext() && img.canvas === editor.canvas) {
                  editor.canvas.setActiveObject(img);
                  editor.canvas.renderAll();
                }

                console.log(`Image replaced for ${layer.id} at index ${objectIndex}`, img);
              } catch (selectionError) {
                console.warn('Error selecting new image:', selectionError);
                // Still render the canvas even if selection fails
                if (editor?.canvas) {
                  editor.canvas.renderAll();
                }
              }
            }, 100);
          } catch (renderError) {
            console.error('Error during image replacement:', renderError);
            // Fallback: just render the canvas
            if (editor?.canvas) {
              editor.canvas.renderAll();
            }
          }
        }, {
          crossOrigin: 'anonymous'
        });
      }
    });

    // Reset the flag after processing
    isApplyingCustomizations.current = false;

    // Refresh object locking state after customizations are applied
    refreshObjectLocking();

    // Generate preview after customizations are applied
    setTimeout(() => {
      generatePreview();
    }, 200);
    }, 150), // 150ms debounce - faster response for better UX
    [editor, templateData.editableLayers, customizations, generatePreview]
  );

  // Function to refresh object locking state
  const refreshObjectLocking = useCallback(() => {
    if (!editor?.canvas) return;

    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);
    const allObjects = editor.canvas.getObjects();

    allObjects.forEach((obj: any) => {
      const isEditable = editableLayerIds.includes(obj.id);
      const isWorkspace = obj.name === 'clip';

      if (!isEditable) {
        // Lock the object - make it non-selectable and non-interactive
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
        obj.lockMovementX = true;
        obj.lockMovementY = true;
        obj.lockRotation = true;
        obj.lockScalingX = true;
        obj.lockScalingY = true;
        obj.lockUniScaling = true;
        obj.lockSkewingX = true;
        obj.lockSkewingY = true;
      } else if (isEditable) {
        // Ensure editable objects are interactive
        obj.selectable = true;
        obj.evented = true;
        obj.hasControls = true;
        obj.hasBorders = true;
        // Remove any locks on editable objects
        obj.lockMovementX = false;
        obj.lockMovementY = false;
        obj.lockRotation = false;
        obj.lockScalingX = false;
        obj.lockScalingY = false;
        obj.lockUniScaling = false;
        obj.lockSkewingX = false;
        obj.lockSkewingY = false;
      }
    });

    editor.canvas.renderAll();
  }, [editor, templateData.editableLayers]);

  // Apply customizations from sidebar to canvas
  useEffect(() => {
    applyCustomizations();
  }, [customizations, applyCustomizations]);

  // Listen for download and preview events from parent component
  useEffect(() => {
    const handleDownloadEvent = (event: CustomEvent) => {
      console.log('Download event received in CustomizationEditor:', event.detail);
      const { filename, quality, format } = event.detail;

      // Call generateDownload directly to avoid dependency issues
      if (!editor?.canvas) {
        console.error('No canvas available for download');
        return;
      }

      try {
        console.log('Generating high-quality download...', {
          canvasObjects: editor.canvas.getObjects().length,
          canvasSize: { width: editor.canvas.width, height: editor.canvas.height }
        });

        // Save current viewport transform and zoom
        const currentTransform = editor.canvas.viewportTransform?.slice();
        const currentZoom = editor.canvas.getZoom();

        // Reset viewport transform and zoom to get the full canvas at original scale
        editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
        editor.canvas.setZoom(1);

        // Find the workspace (clip) object to get proper dimensions
        const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
        console.log('Workspace found:', !!workspace, workspace ? {
          left: workspace.left,
          top: workspace.top,
          width: workspace.width,
          height: workspace.height,
          scaleX: workspace.scaleX,
          scaleY: workspace.scaleY
        } : 'none');

        let dataUrl: string;

        if (workspace) {
          // Get the workspace bounding rectangle which accounts for position and scale
          const workspaceBounds = workspace.getBoundingRect();

          console.log('Workspace bounds:', {
            left: workspaceBounds.left,
            top: workspaceBounds.top,
            width: workspaceBounds.width,
            height: workspaceBounds.height
          });

          // Generate high-quality image with exact workspace bounds
          dataUrl = editor.canvas.toDataURL({
            format: format,
            quality: quality,
            multiplier: 2, // Higher resolution for download
            left: workspaceBounds.left,
            top: workspaceBounds.top,
            width: workspaceBounds.width,
            height: workspaceBounds.height,
          });
        } else {
          // Fallback: try to find template bounds from all objects
          console.log('No workspace found, calculating bounds from all objects');
          const allObjects = editor.canvas.getObjects().filter((obj: any) => obj.name !== 'clip');

          if (allObjects.length > 0) {
            // Calculate bounding box of all objects
            let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

            allObjects.forEach((obj: any) => {
              const bounds = obj.getBoundingRect();
              minX = Math.min(minX, bounds.left);
              minY = Math.min(minY, bounds.top);
              maxX = Math.max(maxX, bounds.left + bounds.width);
              maxY = Math.max(maxY, bounds.top + bounds.height);
            });

            const boundingWidth = maxX - minX;
            const boundingHeight = maxY - minY;

            console.log('Calculated bounds:', { minX, minY, width: boundingWidth, height: boundingHeight });

            dataUrl = editor.canvas.toDataURL({
              format: format,
              quality: quality,
              multiplier: 2,
              left: minX,
              top: minY,
              width: boundingWidth,
              height: boundingHeight,
            });
          } else {
            // Final fallback to full canvas
            console.log('Using full canvas for download');
            dataUrl = editor.canvas.toDataURL({
              format: format,
              quality: quality,
              multiplier: 2,
            });
          }
        }

        // Restore viewport transform and zoom
        if (currentTransform) {
          editor.canvas.setViewportTransform(currentTransform);
        }
        editor.canvas.setZoom(currentZoom);

        console.log('Generated dataURL length:', dataUrl.length);

        // Trigger download
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('Download triggered successfully for file:', filename);
      } catch (error) {
        console.error('Failed to generate download:', error);
      }
    };

    const handlePreviewEvent = () => {
      console.log('Preview event received in CustomizationEditor');

      // Call generatePreview directly to avoid dependency issues
      if (!editor?.canvas) return;

      try {
        const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
        if (!workspace) return;

        const dataUrl = editor.canvas.toDataURL({
          format: 'png',
          quality: 0.9,
          multiplier: 0.5,
        });
        onPreviewGenerated(dataUrl);
      } catch (error) {
        console.error('Failed to generate preview:', error);
      }
    };

    console.log('Setting up event listeners for download and preview');
    window.addEventListener('downloadCustomized', handleDownloadEvent as EventListener);
    window.addEventListener('generatePreview', handlePreviewEvent);

    return () => {
      console.log('Cleaning up event listeners');
      window.removeEventListener('downloadCustomized', handleDownloadEvent as EventListener);
      window.removeEventListener('generatePreview', handlePreviewEvent);
    };
  }, [editor, onPreviewGenerated]);

  // Ensure canvas objects have proper IDs when editor loads
  useEffect(() => {
    if (!editor?.canvas) return;

    const canvas = editor.canvas;
    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);

    console.log('Setting up canvas object IDs for editable layers:', editableLayerIds);

    // Wait a bit for the canvas to be fully loaded
    setTimeout(() => {
      const allObjects = canvas.getObjects();
      console.log(`Canvas loaded with ${allObjects.length} objects`);

      // Log only editable objects for debugging
      const editableObjects = allObjects.filter((obj: any) =>
        editableLayerIds.includes(obj.id) ||
        obj.type === 'textbox' ||
        obj.type === 'image'
      );
      console.log('Editable/relevant objects:', editableObjects.map((obj: any) => ({
        id: obj.id,
        type: obj.type,
        name: obj.name,
        text: obj.type === 'textbox' ? obj.text : undefined
      })));

      // Try to match objects to editable layers using multiple criteria
      templateData.editableLayers.forEach((layer) => {
        let matchedObject = allObjects.find((obj: any) => obj.id === layer.id);

        if (!matchedObject) {
          if (layer.type === 'text') {
            // Try to find by text content if no ID match
            matchedObject = allObjects.find((obj: any) =>
              obj.type === 'textbox' &&
              obj.text === layer.originalValue &&
              !editableLayerIds.includes(obj.id)
            );
          } else if (layer.type === 'image') {
            // For images, use more sophisticated matching
            const imageObjects = allObjects.filter((obj: any) =>
              obj.type === 'image' && !editableLayerIds.includes(obj.id)
            );

            console.log(`Trying to match image layer ${layer.id} (${layer.name}) with ${imageObjects.length} available image objects`);
            console.log('Available image objects:', imageObjects.map((obj: any) => ({
              id: obj.id,
              left: obj.left,
              top: obj.top,
              width: obj.width,
              height: obj.height,
              scaleX: obj.scaleX,
              scaleY: obj.scaleY,
              uniqueId: createImageUniqueId(obj)
            })));

            // Strategy 1: Try to match by stored properties if available in originalValue
            if (layer.originalValue) {
              try {
                const originalProps = JSON.parse(layer.originalValue);
                console.log(`Using stored properties for matching layer ${layer.id}:`, originalProps);

                // First try exact position and size match
                matchedObject = imageObjects.find((obj: any) => doImagesMatch(obj, originalProps, 2));

                if (matchedObject) {
                  console.log(`Matched image by exact position/size for layer ${layer.id}`);
                } else {
                  // Try matching by unique identifier
                  matchedObject = imageObjects.find((obj: any) => {
                    const objUniqueId = createImageUniqueId(obj);
                    return objUniqueId === originalProps.uniqueId;
                  });

                  if (matchedObject) {
                    console.log(`Matched image by unique identifier for layer ${layer.id}`);
                  } else {
                    // Try matching by source URL if available
                    if (originalProps.src) {
                      matchedObject = imageObjects.find((obj: any) => {
                        const objSrc = (obj as fabric.Image).getSrc?.();
                        return objSrc === originalProps.src;
                      });

                      if (matchedObject) {
                        console.log(`Matched image by source URL for layer ${layer.id}`);
                      }
                    }
                  }
                }
              } catch (e) {
                console.warn(`Failed to parse originalValue for layer ${layer.id}:`, e);
              }
            }

            // Strategy 2: If no property match, try to match by position similarity (looser matching)
            if (!matchedObject) {
              console.log(`Trying position similarity matching for layer ${layer.id}`);

              if (layer.originalValue) {
                try {
                  const originalProps = JSON.parse(layer.originalValue);

                  // Find the closest image by position
                  let closestDistance = Infinity;
                  let closestObject = null;

                  imageObjects.forEach((obj: any) => {
                    const distance = Math.sqrt(
                      Math.pow(obj.left - originalProps.left, 2) +
                      Math.pow(obj.top - originalProps.top, 2)
                    );

                    if (distance < closestDistance) {
                      closestDistance = distance;
                      closestObject = obj;
                    }
                  });

                  // Only use closest match if it's reasonably close (within 50 pixels)
                  if (closestObject && closestDistance < 50) {
                    matchedObject = closestObject;
                    console.log(`Matched image by closest position (distance: ${closestDistance.toFixed(2)}) for layer ${layer.id}`);
                  }
                } catch (e) {
                  // Continue to next strategy
                }
              }
            }

            // Strategy 3: If still no match, try to match by sorted index (as fallback)
            if (!matchedObject) {
              const imageLayerIndex = templateData.editableLayers
                .filter(l => l.type === 'image')
                .indexOf(layer);

              // Sort image objects by their position (top-to-bottom, left-to-right) for consistent ordering
              const sortedImageObjects = imageObjects.sort((a: any, b: any) => {
                const topDiff = a.top - b.top;
                if (Math.abs(topDiff) > 10) return topDiff; // Different rows
                return a.left - b.left; // Same row, sort by left position
              });

              if (sortedImageObjects[imageLayerIndex]) {
                matchedObject = sortedImageObjects[imageLayerIndex];
                console.log(`Matched image by sorted index ${imageLayerIndex} for layer ${layer.id}`);
              }
            }

            // Strategy 4: Last resort - take first available image but warn about it
            if (!matchedObject && imageObjects.length > 0) {
              matchedObject = imageObjects[0];
              console.warn(`FALLBACK: Using first available image for layer ${layer.id} - this may not be correct!`);
            }
          }
        }

        if (matchedObject && !(matchedObject as any).id) {
          console.log(`Assigning ID ${layer.id} to ${layer.type} object:`, {
            type: matchedObject.type,
            left: matchedObject.left,
            top: matchedObject.top,
            width: matchedObject.width,
            height: matchedObject.height
          });
          (matchedObject as any).id = layer.id;
        } else if (!matchedObject) {
          console.warn(`Could not find matching object for layer ${layer.id} (${layer.name})`);
        }
      });

      // Lock all non-editable objects in public customization interface
      console.log('Locking non-editable objects...');
      allObjects.forEach((obj: any) => {
        const isEditable = editableLayerIds.includes(obj.id);
        const isWorkspace = obj.name === 'clip';

        if (!isEditable) {
          // Lock the object - make it non-selectable and non-interactive
          obj.selectable = false;
          obj.evented = false;
          obj.hasControls = false;
          obj.hasBorders = false;
          obj.lockMovementX = true;
          obj.lockMovementY = true;
          obj.lockRotation = true;
          obj.lockScalingX = true;
          obj.lockScalingY = true;
          obj.lockUniScaling = true;
          obj.lockSkewingX = true;
          obj.lockSkewingY = true;

          if (isWorkspace) {
            console.log(`Locked workspace object: ${obj.type}`);
          } else {
            console.log(`Locked non-editable object: ${obj.type} (ID: ${obj.id || 'no-id'})`);
          }
        } else if (isEditable) {
          // Ensure editable objects are interactive
          obj.selectable = true;
          obj.evented = true;
          obj.hasControls = true;
          obj.hasBorders = true;

          console.log(`Enabled interaction for editable object: ${obj.type} (ID: ${obj.id})`);
        }
      });

      try {
        canvas.renderAll();

        // Only trigger autoZoom if canvas is still loading (prevent multiple calls)
        if (isCanvasLoading) {
          setTimeout(() => {
            if (editor?.autoZoom && isCanvasLoading) {
              console.log('Auto-zooming after template load and ID assignment');
              editor.autoZoom();
              // Ensure loading state is cleared
              setIsCanvasLoading(false);
            }
          }, 300);
        }
      } catch (error) {
        console.error('Error rendering canvas after ID assignment:', error);
      }
    }, 500);
  }, [editor, templateData.editableLayers]);

  // Function to select object by layer ID (called from parent)
  useEffect(() => {
    if (!editor?.canvas) return;

    console.log('External active layer changed:', externalActiveLayerId);

    if (externalActiveLayerId) {
      const allObjects = editor.canvas.getObjects();
      console.log('All canvas objects:', allObjects.map((obj: any) => ({ id: obj.id, type: obj.type, name: obj.name })));

      const targetObject = allObjects.find((obj: any) => obj.id === externalActiveLayerId);
      console.log('Found target object:', targetObject);

      if (targetObject) {
        console.log('Selecting object in canvas:', (targetObject as any).id);
        editor.canvas.setActiveObject(targetObject);
        editor.canvas.renderAll();
      } else {
        console.log('Target object not found for ID:', externalActiveLayerId);
      }
    } else {
      // Clear selection if no layer is active
      console.log('Clearing canvas selection');
      editor.canvas.discardActiveObject();
      editor.canvas.renderAll();
    }
  }, [editor, externalActiveLayerId]);

  return (
    <div className="customization-editor-container w-full h-full flex flex-col bg-muted">
      <Toolbar
        editor={editor}
        activeTool={activeTool}
        onChangeActiveTool={handleToolChange}
        key={JSON.stringify(editor?.canvas.getActiveObject())}
      />
      <div
        className="customization-canvas-container flex-1 bg-muted overflow-hidden relative w-full"
        ref={containerRef}
        style={{ minHeight: '400px' }}
      >
        <div className={`absolute inset-0 flex items-center justify-center bg-muted/80 z-10 ${isCanvasLoading ? 'block' : 'hidden'}`}>
          <div className="flex flex-col items-center space-y-3 bg-white p-6 rounded-lg shadow-lg">
            <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
            <div className="text-center">
              <p className="text-lg font-medium text-gray-900">Loading Template</p>
              <p className="text-sm text-gray-600">Please wait while we prepare your editor...</p>
              <p className="text-xs text-gray-500 mt-2">
                Debug: {editor ? 'Editor ready' : 'Editor loading'} |
                Objects: {editor?.canvas?.getObjects().length || 0}
              </p>
            </div>
          </div>
        </div>
        <div className={`absolute inset-0 flex items-center justify-center bg-black/50 z-20 ${isRemovingBackground ? 'block' : 'hidden'}`}>
          <div className="flex flex-col items-center space-y-3 bg-white p-6 rounded-lg shadow-lg">
            <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
            <div className="text-center">
              <p className="text-lg font-medium text-gray-900">Removing Background</p>
              <p className="text-sm text-gray-600">Please wait while we process your image...</p>
            </div>
          </div>
        </div>
        <canvas
          key={`canvas-${templateData.id}`}
          ref={canvasRef}
          className="block w-full h-full"
          style={{
            display: 'block',
            margin: '0 auto',
            maxWidth: '100%',
            maxHeight: '100%',
          }}
        />
      </div>
      <Footer editor={editor} />
    </div>
  );
};
