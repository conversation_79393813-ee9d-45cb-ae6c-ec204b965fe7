// Client-side AI service for browser-based fallbacks
let transformersLoaded = false
let pipeline = null
let transformersPromise = null

// Check if client-side AI is available
export function isClientSideAiAvailable() {
  return typeof window !== 'undefined' && 'WebGL2RenderingContext' in window
}

export function isClientSideBackgroundRemovalAvailable() {
  return isClientSideAiAvailable() && typeof OffscreenCanvas !== 'undefined'
}

// Lazy loading for Transformers.js
export async function loadTransformers() {
  if (!isClientSideAiAvailable()) {
    throw new Error('Client-side AI not available - WebGL2 required')
  }

  if (!transformersPromise) {
    transformersPromise = import('@xenova/transformers').then(async ({ pipeline: createPipeline }) => {
      try {
        // Load image segmentation pipeline for background removal
        const pipe = await createPipeline('image-segmentation', 'Xenova/detr-resnet-50-panoptic')
        transformersLoaded = true
        pipeline = pipe
        return pipe
      } catch (error) {
        console.error('Failed to load Transformers.js pipeline:', error)
        throw error
      }
    })
  }
  
  return transformersPromise
}

// Client-side background removal using Transformers.js
export async function removeBackgroundClientSide(imageUrl) {
  if (!isClientSideBackgroundRemovalAvailable()) {
    throw new Error('Client-side background removal not available')
  }

  try {
    const pipe = await loadTransformers()
    
    // Create image element
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    return new Promise((resolve, reject) => {
      img.onload = async () => {
        try {
          // Create canvas for processing
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          
          canvas.width = img.width
          canvas.height = img.height
          
          // Draw image to canvas
          ctx.drawImage(img, 0, 0)
          
          // Get image data
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
          
          // Process with Transformers.js (simplified implementation)
          // Note: This is a basic implementation - real background removal would be more complex
          const result = await pipe(canvas.toDataURL())
          
          // For now, return the original image with a simple filter applied
          // In a real implementation, you would process the segmentation results
          ctx.globalCompositeOperation = 'multiply'
          ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          
          resolve(canvas.toDataURL('image/png'))
        } catch (error) {
          reject(error)
        }
      }
      
      img.onerror = reject
      img.src = imageUrl
    })
  } catch (error) {
    console.error('Client-side background removal failed:', error)
    throw error
  }
}

// Client-side image upscaling using canvas
export async function upscaleImageClientSide(imageUrl, scale = 2) {
  if (!isClientSideAiAvailable()) {
    throw new Error('Client-side image processing not available')
  }

  if (scale < 1 || scale > 4) {
    throw new Error('Scale must be between 1 and 4')
  }

  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        // Set new dimensions
        canvas.width = img.width * scale
        canvas.height = img.height * scale
        
        // Use image smoothing for better quality
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'
        
        // Draw scaled image
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        resolve(canvas.toDataURL('image/png'))
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = reject
    img.src = imageUrl
  })
}

// Client-side image filters
export const CLIENT_SIDE_FILTERS = {
  BRIGHTNESS: 'brightness',
  CONTRAST: 'contrast',
  SATURATION: 'saturate',
  BLUR: 'blur',
  GRAYSCALE: 'grayscale',
  SEPIA: 'sepia',
  INVERT: 'invert',
  HUE_ROTATE: 'hue-rotate'
}

export async function applyImageFilterClientSide(imageUrl, filter, intensity = 1) {
  if (!isClientSideAiAvailable()) {
    throw new Error('Client-side image processing not available')
  }

  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        canvas.width = img.width
        canvas.height = img.height
        
        // Apply CSS filter
        let filterValue = ''
        switch (filter) {
          case CLIENT_SIDE_FILTERS.BRIGHTNESS:
            filterValue = `brightness(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.CONTRAST:
            filterValue = `contrast(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.SATURATION:
            filterValue = `saturate(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.BLUR:
            filterValue = `blur(${intensity}px)`
            break
          case CLIENT_SIDE_FILTERS.GRAYSCALE:
            filterValue = `grayscale(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.SEPIA:
            filterValue = `sepia(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.INVERT:
            filterValue = `invert(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.HUE_ROTATE:
            filterValue = `hue-rotate(${intensity * 360}deg)`
            break
          default:
            filterValue = 'none'
        }
        
        ctx.filter = filterValue
        ctx.drawImage(img, 0, 0)
        
        resolve(canvas.toDataURL('image/png'))
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = reject
    img.src = imageUrl
  })
}

// Resize image for performance
export async function resizeImageClientSide(imageUrl, maxWidth = 2048, maxHeight = 2048, quality = 0.9) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      try {
        // Check if resize is needed
        if (img.width <= maxWidth && img.height <= maxHeight) {
          resolve(imageUrl)
          return
        }
        
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        // Calculate new dimensions maintaining aspect ratio
        const ratio = Math.min(maxWidth / img.width, maxHeight / img.height)
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio
        
        // Use high quality scaling
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'
        
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        resolve(canvas.toDataURL('image/jpeg', quality))
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = reject
    img.src = imageUrl
  })
}

// Get available client-side features
export function getClientSideCapabilities() {
  return {
    available: isClientSideAiAvailable(),
    backgroundRemoval: isClientSideBackgroundRemovalAvailable(),
    upscaling: isClientSideAiAvailable(),
    filters: isClientSideAiAvailable(),
    resize: isClientSideAiAvailable(),
    webgl2: typeof window !== 'undefined' && 'WebGL2RenderingContext' in window,
    offscreenCanvas: typeof OffscreenCanvas !== 'undefined',
    transformers: transformersLoaded
  }
}

// Preload Transformers.js (optional)
export async function preloadTransformers() {
  if (isClientSideAiAvailable()) {
    try {
      await loadTransformers()
      console.log('Transformers.js preloaded successfully')
    } catch (error) {
      console.warn('Failed to preload Transformers.js:', error)
    }
  }
}
