// Client-side AI service for browser-based fallbacks
// Note: Transformers.js removed to avoid build issues with Next.js

// Check if client-side AI is available
export function isClientSideAiAvailable() {
  return typeof window !== 'undefined' && 'WebGL2RenderingContext' in window
}

export function isClientSideBackgroundRemovalAvailable() {
  return isClientSideAiAvailable() && typeof HTMLCanvasElement !== 'undefined'
}

// Transformers.js loading disabled to avoid build issues
export async function loadTransformers() {
  throw new Error('Transformers.js disabled to avoid build issues with Next.js. Use API-based processing instead.')
}

// Client-side background removal using simple image processing
export async function removeBackgroundClientSide(imageUrl) {
  if (!isClientSideBackgroundRemovalAvailable()) {
    throw new Error('Client-side background removal not available')
  }

  try {
    // Create image element
    const img = new Image()
    img.crossOrigin = 'anonymous'

    return new Promise((resolve, reject) => {
      img.onload = async () => {
        try {
          // Create canvas for processing
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')

          canvas.width = img.width
          canvas.height = img.height

          // Draw image to canvas
          ctx.drawImage(img, 0, 0)

          // Get image data for processing
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
          const data = imageData.data

          // Simple background removal using color similarity
          // This is a basic implementation - for production use API-based solutions
          for (let i = 0; i < data.length; i += 4) {
            const r = data[i]
            const g = data[i + 1]
            const b = data[i + 2]

            // Simple background detection (adjust thresholds as needed)
            const isBackground = (
              (r > 240 && g > 240 && b > 240) || // White background
              (r < 15 && g < 15 && b < 15) ||    // Black background
              (Math.abs(r - g) < 10 && Math.abs(g - b) < 10 && Math.abs(r - b) < 10) // Gray background
            )

            if (isBackground) {
              data[i + 3] = 0 // Make transparent
            }
          }

          // Put processed data back
          ctx.putImageData(imageData, 0, 0)

          resolve(canvas.toDataURL('image/png'))
        } catch (error) {
          reject(error)
        }
      }

      img.onerror = reject
      img.src = imageUrl
    })
  } catch (error) {
    console.error('Client-side background removal failed:', error)
    throw error
  }
}

// Client-side image upscaling using canvas
export async function upscaleImageClientSide(imageUrl, scale = 2) {
  if (!isClientSideAiAvailable()) {
    throw new Error('Client-side image processing not available')
  }

  if (scale < 1 || scale > 4) {
    throw new Error('Scale must be between 1 and 4')
  }

  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        // Set new dimensions
        canvas.width = img.width * scale
        canvas.height = img.height * scale
        
        // Use image smoothing for better quality
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'
        
        // Draw scaled image
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        resolve(canvas.toDataURL('image/png'))
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = reject
    img.src = imageUrl
  })
}

// Client-side image filters
export const CLIENT_SIDE_FILTERS = {
  BRIGHTNESS: 'brightness',
  CONTRAST: 'contrast',
  SATURATION: 'saturate',
  BLUR: 'blur',
  GRAYSCALE: 'grayscale',
  SEPIA: 'sepia',
  INVERT: 'invert',
  HUE_ROTATE: 'hue-rotate'
}

export async function applyImageFilterClientSide(imageUrl, filter, intensity = 1) {
  if (!isClientSideAiAvailable()) {
    throw new Error('Client-side image processing not available')
  }

  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        canvas.width = img.width
        canvas.height = img.height
        
        // Apply CSS filter
        let filterValue = ''
        switch (filter) {
          case CLIENT_SIDE_FILTERS.BRIGHTNESS:
            filterValue = `brightness(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.CONTRAST:
            filterValue = `contrast(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.SATURATION:
            filterValue = `saturate(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.BLUR:
            filterValue = `blur(${intensity}px)`
            break
          case CLIENT_SIDE_FILTERS.GRAYSCALE:
            filterValue = `grayscale(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.SEPIA:
            filterValue = `sepia(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.INVERT:
            filterValue = `invert(${intensity})`
            break
          case CLIENT_SIDE_FILTERS.HUE_ROTATE:
            filterValue = `hue-rotate(${intensity * 360}deg)`
            break
          default:
            filterValue = 'none'
        }
        
        ctx.filter = filterValue
        ctx.drawImage(img, 0, 0)
        
        resolve(canvas.toDataURL('image/png'))
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = reject
    img.src = imageUrl
  })
}

// Resize image for performance
export async function resizeImageClientSide(imageUrl, maxWidth = 2048, maxHeight = 2048, quality = 0.9) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      try {
        // Check if resize is needed
        if (img.width <= maxWidth && img.height <= maxHeight) {
          resolve(imageUrl)
          return
        }
        
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        // Calculate new dimensions maintaining aspect ratio
        const ratio = Math.min(maxWidth / img.width, maxHeight / img.height)
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio
        
        // Use high quality scaling
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'
        
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        resolve(canvas.toDataURL('image/jpeg', quality))
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = reject
    img.src = imageUrl
  })
}

// Get available client-side features
export function getClientSideCapabilities() {
  return {
    available: isClientSideAiAvailable(),
    backgroundRemoval: isClientSideBackgroundRemovalAvailable(),
    upscaling: isClientSideAiAvailable(),
    filters: isClientSideAiAvailable(),
    resize: isClientSideAiAvailable(),
    webgl2: typeof window !== 'undefined' && 'WebGL2RenderingContext' in window,
    canvas: typeof HTMLCanvasElement !== 'undefined',
    transformers: false // Disabled to avoid build issues
  }
}

// Preload Transformers.js (optional) - Currently disabled
export async function preloadTransformers() {
  // Disabled to avoid build issues with Next.js
  console.log('Transformers.js preloading disabled to avoid build issues')
  return Promise.resolve()
}
