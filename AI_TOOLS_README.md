# AI Tools Implementation - Canva Clone

This document provides an overview of the AI tools implementation for the Canva clone project. The system provides comprehensive AI-powered image generation, editing, and enhancement capabilities with multi-provider support and fallback mechanisms.

## 🚀 Features Implemented

### ✅ Core AI Services
- **Multi-provider AI support** (Together AI, Fal.ai, Replicate)
- **Image generation** from text prompts
- **Background removal** with multiple providers
- **Image upscaling** and enhancement
- **Style filters** and image variations
- **Client-side fallbacks** using Transformers.js

### ✅ User Interface
- **Enhanced AI Panel** with tabbed interface
- **Improved AI Sidebar** with advanced options
- **Updated Remove Background Sidebar** with provider selection
- **Real-time API status monitoring**
- **Responsive design** with loading states

### ✅ Performance & Reliability
- **Image caching** with LRU cache implementation
- **Retry logic** with exponential backoff
- **Error handling** and graceful degradation
- **Performance monitoring** and analytics
- **Memory usage optimization**

### ✅ Security & Validation
- **Input sanitization** for prompts
- **API key validation** and secure storage
- **CORS proxy** for image handling
- **Rate limiting** protection
- **Content filtering** and safety checks

## 📁 File Structure

```
src/
├── services/
│   ├── aiService.js              # Main AI service with multi-provider support
│   └── clientAiService.js        # Browser-based fallbacks using Transformers.js
├── components/
│   ├── editor/panels/
│   │   └── AiPanel.jsx           # Enhanced AI panel with tabs
│   └── ui/
│       ├── tabs.tsx              # Radix UI tabs component
│       └── select.tsx            # Radix UI select component
├── fabric/
│   └── fabric-utils.js           # Canvas integration utilities
├── utils/
│   └── imageCache.js             # Image caching and performance utilities
├── app/api/
│   ├── proxy-image/
│   │   └── route.js              # Image proxy for CORS handling
│   └── [[...route]]/
│       └── ai.ts                 # Enhanced AI API routes
└── features/editor/components/
    ├── ai-sidebar.tsx            # Enhanced AI sidebar
    └── remove-bg-sidebar.tsx     # Enhanced background removal sidebar
```

## 🔧 Setup Instructions

### 1. Install Dependencies

The required dependencies have been installed:

```bash
npm install @fal-ai/client @xenova/transformers together-ai @radix-ui/react-tabs @radix-ui/react-select
```

### 2. Environment Configuration

Update your `.env.local` file with the following API keys:

```env
# Together AI (Primary Provider)
TOGETHER_API_KEY=your-together-api-key-here
NEXT_PUBLIC_TOGETHER_API_KEY=your-together-api-key-here

# Fal.ai
FAL_KEY=your-fal-api-key-here
NEXT_PUBLIC_FAL_KEY=your-fal-api-key-here

# Replicate (Legacy Support)
REPLICATE_API_TOKEN=your-replicate-api-token-here
NEXT_PUBLIC_REPLICATE_API_TOKEN=your-replicate-api-token-here
```

### 3. API Key Setup

#### Together AI (Recommended)
1. Sign up at [Together.ai](https://together.ai)
2. Navigate to API settings
3. Generate API key
4. Add to environment variables

#### Fal.ai
1. Sign up at [Fal.ai](https://fal.ai)
2. Go to account settings
3. Generate API key
4. Add to environment variables

#### Replicate
1. Sign up at [Replicate.com](https://replicate.com)
2. Go to account settings
3. Generate API token
4. Add to environment variables

## 🎯 Usage Examples

### Generate Image
```javascript
import { generateImage } from '@/services/aiService'

const imageUrl = await generateImage('a beautiful sunset', {
  width: 1024,
  height: 1024,
  steps: 28,
  guidance: 7.5
})
```

### Remove Background
```javascript
import { removeBackground } from '@/services/aiService'

const processedUrl = await removeBackground(imageUrl, 'fal')
```

### Add Image to Canvas
```javascript
import { addImageToCanvas } from '@/fabric/fabric-utils'

const fabricImage = await addImageToCanvas(canvas, imageUrl)
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Coverage
- Unit tests for AI service functions
- Integration tests for UI components
- Mock implementations for external APIs
- Performance and error handling tests

## 🔄 Provider Priority

The system automatically selects providers in this order:

1. **Together AI** (Primary) - Fast, reliable, cost-effective
2. **Fal.ai** (Secondary) - Professional features, high quality
3. **Replicate** (Fallback) - Legacy support, wide model selection

## 🛡️ Fallback Mechanisms

### API Fallbacks
- Automatic provider switching on failure
- Retry logic with exponential backoff
- Graceful error handling and user feedback

### Client-side Fallbacks
- Transformers.js for offline background removal
- Canvas-based image processing
- WebGL2 detection and compatibility checks

## 📊 Performance Features

### Image Caching
- LRU cache with configurable size limits
- Automatic cleanup of expired entries
- Cache statistics and monitoring

### Performance Monitoring
- Request timing and success rates
- Memory usage tracking
- Error rate monitoring

### Optimization
- Image resizing before processing
- Lazy loading of heavy dependencies
- Debounced user inputs

## 🔒 Security Features

### Input Validation
- Prompt sanitization (XSS protection)
- Length limits and content filtering
- API parameter validation

### API Security
- Server-side API key validation
- CORS proxy for secure image handling
- Rate limiting and abuse prevention

## 🚨 Troubleshooting

### Common Issues

#### CORS Issues
Use the proxy route for external images:
```javascript
const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`
```

#### API Rate Limits
The system includes automatic retry with exponential backoff.

#### Memory Issues
Images are automatically resized if they exceed size limits.

### Debug Mode
Set `NODE_ENV=development` to enable debug logging.

## 🔮 Future Enhancements

### Planned Features
- [ ] Image-to-image generation with FLUX Kontext models
- [ ] Advanced style transfer and artistic filters
- [ ] Batch processing capabilities
- [ ] Custom model fine-tuning
- [ ] Advanced prompt engineering tools

### Performance Improvements
- [ ] WebWorker support for heavy processing
- [ ] Progressive image loading
- [ ] Advanced caching strategies
- [ ] CDN integration for generated images

## 📚 API Reference

### Main Functions

#### `generateImage(prompt, options)`
Generates an image from a text prompt.

#### `removeBackground(imageUrl, provider)`
Removes background from an image.

#### `upscaleImage(imageUrl, scale, provider)`
Upscales an image by the specified factor.

#### `generateImageVariations(imageUrl, prompt, options)`
Creates variations of an existing image.

#### `applyStyleFilter(imageUrl, style, customPrompt)`
Applies artistic style filters to an image.

### Utility Functions

#### `isApiConfigured(provider)`
Checks if an API provider is properly configured.

#### `getAvailableModels(category)`
Returns available AI models for a specific category.

#### `getStyleFilters()`
Returns available style filters.

## 🤝 Contributing

When contributing to the AI tools:

1. Follow the existing code structure
2. Add tests for new functionality
3. Update documentation
4. Ensure security best practices
5. Test with multiple providers

## 📄 License

This AI tools implementation is part of the Canva clone project and follows the same licensing terms.

---

For more detailed information, refer to the complete documentation files:
- `AI_TOOLS_QUICK_REFERENCE.md` - Quick setup and usage guide
- `AI_TOOLS_COMPLETE_DOCUMENTATION.md` - Comprehensive technical documentation
