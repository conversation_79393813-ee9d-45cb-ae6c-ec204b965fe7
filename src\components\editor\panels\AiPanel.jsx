import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Wand2, 
  Image as ImageIcon, 
  Sparkles, 
  Settings, 
  Loader2, 
  AlertCircle,
  CheckCircle,
  XCircle,
  Info
} from "lucide-react"

import { useGenerateImage } from "@/features/ai/api/use-generate-image"
import { useRemoveBg } from "@/features/ai/api/use-remove-bg"
import { addImageToCanvas, getSelectedImageUrl, applyProcessedImageToSelected } from "@/fabric/fabric-utils"
import { 
  isApiConfigured, 
  getAvailableModels, 
  getStyleFilters, 
  AI_PROVIDERS 
} from "@/services/aiService"
import { 
  isClientSideAiAvailable, 
  isClientSideBackgroundRemovalAvailable,
  getClientSideCapabilities 
} from "@/services/clientAiService"

export default function AiPanel({ canvas, selectedObject, onClose }) {
  const [activeTab, setActiveTab] = useState("generate")
  const [prompt, setPrompt] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationOptions, setGenerationOptions] = useState({
    width: 1024,
    height: 1024,
    steps: 28,
    guidance: 7.5
  })
  const [availableModels, setAvailableModels] = useState([])
  const [styleFilters, setStyleFilters] = useState([])
  const [apiStatus, setApiStatus] = useState({})
  const [clientCapabilities, setClientCapabilities] = useState({})

  const generateImageMutation = useGenerateImage()
  const removeBgMutation = useRemoveBg()

  // Load initial data
  useEffect(() => {
    loadAvailableModels()
    loadStyleFilters()
    checkApiStatus()
    checkClientCapabilities()
  }, [])

  const loadAvailableModels = async () => {
    try {
      const models = getAvailableModels('text-to-image')
      setAvailableModels(models)
    } catch (error) {
      console.error('Failed to load models:', error)
    }
  }

  const loadStyleFilters = async () => {
    try {
      const filters = getStyleFilters()
      setStyleFilters(filters)
    } catch (error) {
      console.error('Failed to load style filters:', error)
    }
  }

  const checkApiStatus = async () => {
    try {
      const status = {
        together: isApiConfigured(AI_PROVIDERS.TOGETHER),
        fal: isApiConfigured(AI_PROVIDERS.FAL),
        replicate: isApiConfigured(AI_PROVIDERS.REPLICATE),
        anyConfigured: isApiConfigured()
      }
      setApiStatus(status)
    } catch (error) {
      console.error('Failed to check API status:', error)
    }
  }

  const checkClientCapabilities = () => {
    const capabilities = getClientSideCapabilities()
    setClientCapabilities(capabilities)
  }

  const handleGenerate = async () => {
    if (!prompt.trim()) return

    setIsGenerating(true)
    try {
      const result = await generateImageMutation.mutateAsync({
        prompt,
        ...generationOptions
      })

      if (result.data) {
        await addImageToCanvas(canvas, result.data)
      }
    } catch (error) {
      console.error('Generation failed:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleRemoveBackground = async () => {
    if (!selectedObject || selectedObject.type !== 'image') return

    const imageUrl = getSelectedImageUrl(canvas)
    if (!imageUrl) return

    try {
      const result = await removeBgMutation.mutateAsync({
        image: imageUrl
      })

      if (result.data) {
        await applyProcessedImageToSelected(canvas, result.data)
      }
    } catch (error) {
      console.error('Background removal failed:', error)
    }
  }

  const StatusIndicator = ({ status, label }) => (
    <div className="flex items-center gap-2">
      {status ? (
        <CheckCircle className="w-4 h-4 text-green-500" />
      ) : (
        <XCircle className="w-4 h-4 text-red-500" />
      )}
      <span className="text-sm">{label}</span>
      <Badge variant={status ? "default" : "secondary"}>
        {status ? "Ready" : "Not Configured"}
      </Badge>
    </div>
  )

  return (
    <div className="flex flex-col h-full bg-white border-r">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-500" />
            <h2 className="font-semibold">AI Tools</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            ×
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
          <TabsTrigger value="generate" className="flex items-center gap-1">
            <Wand2 className="w-4 h-4" />
            Generate
          </TabsTrigger>
          <TabsTrigger value="edit" className="flex items-center gap-1">
            <ImageIcon className="w-4 h-4" />
            Edit
          </TabsTrigger>
          <TabsTrigger value="enhance" className="flex items-center gap-1">
            <Sparkles className="w-4 h-4" />
            Enhance
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-1">
            <Settings className="w-4 h-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <ScrollArea className="flex-1">
          <TabsContent value="generate" className="m-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Generate Image</CardTitle>
                <CardDescription>
                  Create images from text descriptions using AI
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="prompt">Prompt</Label>
                  <Textarea
                    id="prompt"
                    placeholder="Describe the image you want to generate..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    rows={4}
                    disabled={isGenerating}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Width</Label>
                    <Input
                      type="number"
                      value={generationOptions.width}
                      onChange={(e) => setGenerationOptions(prev => ({
                        ...prev,
                        width: parseInt(e.target.value) || 1024
                      }))}
                      min={256}
                      max={2048}
                      step={64}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Height</Label>
                    <Input
                      type="number"
                      value={generationOptions.height}
                      onChange={(e) => setGenerationOptions(prev => ({
                        ...prev,
                        height: parseInt(e.target.value) || 1024
                      }))}
                      min={256}
                      max={2048}
                      step={64}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Steps: {generationOptions.steps}</Label>
                  <Slider
                    value={[generationOptions.steps]}
                    onValueChange={([value]) => setGenerationOptions(prev => ({
                      ...prev,
                      steps: value
                    }))}
                    min={10}
                    max={50}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Guidance: {generationOptions.guidance}</Label>
                  <Slider
                    value={[generationOptions.guidance]}
                    onValueChange={([value]) => setGenerationOptions(prev => ({
                      ...prev,
                      guidance: value
                    }))}
                    min={1}
                    max={20}
                    step={0.5}
                  />
                </div>

                <Button
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isGenerating || !apiStatus.anyConfigured}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4 mr-2" />
                      Generate Image
                    </>
                  )}
                </Button>

                {!apiStatus.anyConfigured && (
                  <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <AlertCircle className="w-4 h-4 text-yellow-600" />
                    <span className="text-sm text-yellow-800">
                      No AI providers configured. Check settings tab.
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="edit" className="m-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Edit Image</CardTitle>
                <CardDescription>
                  Modify and enhance selected images
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedObject && selectedObject.type === 'image' ? (
                  <>
                    <Button
                      onClick={handleRemoveBackground}
                      disabled={removeBgMutation.isPending}
                      className="w-full"
                    >
                      {removeBgMutation.isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Removing Background...
                        </>
                      ) : (
                        "Remove Background"
                      )}
                    </Button>
                  </>
                ) : (
                  <div className="flex items-center gap-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
                    <Info className="w-4 h-4 text-gray-600" />
                    <span className="text-sm text-gray-700">
                      Select an image to edit
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="enhance" className="m-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Enhance Image</CardTitle>
                <CardDescription>
                  Apply style filters and improvements
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-600">
                  Enhancement features coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="m-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">AI Provider Status</CardTitle>
                <CardDescription>
                  Check the status of your AI service providers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <StatusIndicator status={apiStatus.together} label="Together AI" />
                <StatusIndicator status={apiStatus.fal} label="Fal.ai" />
                <StatusIndicator status={apiStatus.replicate} label="Replicate" />
                
                <Separator />
                
                <div className="space-y-2">
                  <h4 className="font-medium">Client-side Capabilities</h4>
                  <div className="space-y-1 text-sm">
                    <StatusIndicator 
                      status={clientCapabilities.available} 
                      label="WebGL2 Support" 
                    />
                    <StatusIndicator 
                      status={clientCapabilities.backgroundRemoval} 
                      label="Offline Background Removal" 
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </ScrollArea>
      </Tabs>
    </div>
  )
}
