import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Wand2,
  Image as ImageIcon,
  Sparkles,
  Settings,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Info,
  Scissors,
  ArrowUp,
  Palette,
  Zap,
  Eye,
  RefreshCw
} from "lucide-react"

import { useGenerateImage } from "@/features/ai/api/use-generate-image"
import { useRemoveBg } from "@/features/ai/api/use-remove-bg"
import { useUpscaleImage } from "@/features/ai/api/use-upscale-image"
import { useGenerateVariations } from "@/features/ai/api/use-generate-variations"
import { useApplyStyleFilter } from "@/features/ai/api/use-apply-style-filter"
import { useApiStatus } from "@/features/ai/api/use-api-status"
import { addImageToCanvas, getSelectedImageUrl, applyProcessedImageToSelected } from "@/fabric/fabric-utils"
import {
  isApiConfigured,
  getAvailableModels,
  getStyleFilters,
  AI_PROVIDERS
} from "@/services/aiService"
import {
  isClientSideAiAvailable,
  isClientSideBackgroundRemovalAvailable,
  getClientSideCapabilities
} from "@/services/clientAiService"

export default function AiPanel({ canvas, selectedObject, onClose }) {
  const [activeTab, setActiveTab] = useState("generate")
  const [prompt, setPrompt] = useState("")
  const [variationPrompt, setVariationPrompt] = useState("")
  const [selectedModel, setSelectedModel] = useState("")
  const [selectedProvider, setSelectedProvider] = useState("together")
  const [isGenerating, setIsGenerating] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [generationOptions, setGenerationOptions] = useState({
    width: 1024,
    height: 1024,
    steps: 28,
    guidance: 7.5
  })
  const [upscaleOptions, setUpscaleOptions] = useState({
    scale: 2,
    provider: "clipdrop"
  })
  const [availableModels, setAvailableModels] = useState([])
  const [styleFilters, setStyleFilters] = useState([])
  const [apiStatus, setApiStatus] = useState({})
  const [clientCapabilities, setClientCapabilities] = useState({})

  const generateImageMutation = useGenerateImage()
  const removeBgMutation = useRemoveBg()
  const upscaleImageMutation = useUpscaleImage()
  const generateVariationsMutation = useGenerateVariations()
  const applyStyleFilterMutation = useApplyStyleFilter()
  const apiStatusQuery = useApiStatus()

  // Load initial data
  useEffect(() => {
    loadAvailableModels()
    loadStyleFilters()
    checkApiStatus()
    checkClientCapabilities()
  }, [])

  // Update API status when server data changes
  useEffect(() => {
    if (apiStatusQuery.data?.data) {
      checkApiStatus()
    }
  }, [apiStatusQuery.data])

  const loadAvailableModels = async () => {
    try {
      const models = getAvailableModels('text-to-image')
      setAvailableModels(models)

      // Set default model to FLUX Dev if available and no model is selected
      if (!selectedModel && models.length > 0) {
        const defaultModel = models.find(model => model.default) || models[0]
        setSelectedModel(defaultModel.id)
      }
    } catch (error) {
      console.error('Failed to load models:', error)
    }
  }

  const loadStyleFilters = async () => {
    try {
      const filters = getStyleFilters()
      setStyleFilters(filters)
    } catch (error) {
      console.error('Failed to load style filters:', error)
    }
  }

  const checkApiStatus = async () => {
    try {
      // Use server-side API status if available, otherwise fallback to client-side check
      let status
      if (apiStatusQuery.data?.data) {
        status = apiStatusQuery.data.data
      } else {
        // Fallback to client-side check
        status = {
          together: isApiConfigured(AI_PROVIDERS.TOGETHER),
          fal: isApiConfigured(AI_PROVIDERS.FAL),
          replicate: isApiConfigured(AI_PROVIDERS.REPLICATE),
          clipdrop: isApiConfigured(AI_PROVIDERS.CLIPDROP),
          anyConfigured: isApiConfigured()
        }
      }

      console.log('API Status:', status)
      setApiStatus(status)

      // Update default provider for background removal if ClipDrop is available
      if (status.clipdrop && upscaleOptions.provider !== 'clipdrop') {
        setUpscaleOptions(prev => ({
          ...prev,
          provider: 'clipdrop'
        }))
      }
    } catch (error) {
      console.error('Failed to check API status:', error)
    }
  }

  const checkClientCapabilities = () => {
    const capabilities = getClientSideCapabilities()
    setClientCapabilities(capabilities)
  }

  const handleGenerate = async () => {
    if (!prompt.trim()) return

    setIsGenerating(true)
    try {
      const result = await generateImageMutation.mutateAsync({
        prompt,
        ...generationOptions
      })

      if (result.data && canvas) {
        await addImageToCanvas(canvas, result.data)
      } else if (result.data) {
        console.warn('Canvas not available, cannot add image')
      }
    } catch (error) {
      console.error('Generation failed:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleRemoveBackground = async () => {
    if (!selectedObject || selectedObject.type !== 'image' || !canvas) {
      console.warn('Background removal requires an image to be selected')
      return
    }

    const imageUrl = getSelectedImageUrl(canvas)
    if (!imageUrl) {
      console.warn('Could not get image URL from selected object')
      return
    }

    const provider = upscaleOptions.provider
    console.log('Starting background removal with provider:', provider)

    setIsProcessing(true)
    try {
      const result = await removeBgMutation.mutateAsync({
        image: imageUrl,
        provider: provider
      })

      if (result.data && canvas) {
        console.log('Background removal successful, applying to canvas')
        await applyProcessedImageToSelected(canvas, result.data)
        console.log('Background removal completed successfully!')
      }
    } catch (error) {
      console.error('Background removal failed:', error)
      alert(`Background removal failed: ${error.message}`)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleGenerateVariations = async () => {
    if (!selectedObject || selectedObject.type !== 'image' || !canvas) return

    const imageUrl = getSelectedImageUrl(canvas)
    if (!imageUrl) return

    try {
      const result = await generateVariationsMutation.mutateAsync({
        image: imageUrl,
        prompt: variationPrompt,
        width: generationOptions.width,
        height: generationOptions.height,
        steps: generationOptions.steps,
        guidance: generationOptions.guidance
      })

      if (result.data && canvas) {
        await addImageToCanvas(canvas, result.data)
      }
    } catch (error) {
      console.error('Image variations failed:', error)
    }
  }

  const handleApplyStyleFilter = async (styleId) => {
    if (!selectedObject || selectedObject.type !== 'image' || !canvas) return

    const imageUrl = getSelectedImageUrl(canvas)
    if (!imageUrl) return

    try {
      const result = await applyStyleFilterMutation.mutateAsync({
        image: imageUrl,
        style: styleId
      })

      if (result.data && canvas) {
        await applyProcessedImageToSelected(canvas, result.data)
      }
    } catch (error) {
      console.error('Style filter failed:', error)
    }
  }

  const handleUpscaleImage = async () => {
    // Validate that an image is selected
    if (!selectedObject || selectedObject.type !== 'image') {
      console.warn('No image selected for background removal')
      return
    }

    if (!canvas) {
      console.warn('Canvas not available')
      return
    }

    // Get the image URL from the selected object
    const imageUrl = getSelectedImageUrl(canvas)
    if (!imageUrl) {
      console.warn('Could not get image URL from selected object')
      return
    }

    // Check if the selected provider is available
    const provider = upscaleOptions.provider
    if (!apiStatus[provider]) {
      console.warn(`Provider ${provider} is not configured`)
      return
    }

    console.log('Starting background removal', {
      imageUrl: imageUrl.substring(0, 50) + '...',
      provider,
      selectedObjectType: selectedObject.type
    })

    try {
      const result = await upscaleImageMutation.mutateAsync({
        image: imageUrl,
        scale: upscaleOptions.scale,
        provider: provider
      })

      if (result.data && canvas) {
        console.log('Background removal successful, applying to canvas')
        await applyProcessedImageToSelected(canvas, result.data)
      } else {
        console.warn('No result data received from background removal')
      }
    } catch (error) {
      console.error('Image upscaling failed:', error)
      // You could add a toast notification here for user feedback
    }
  }

  const StatusIndicator = ({ status, label }) => (
    <div className="flex items-center gap-2">
      {status ? (
        <CheckCircle className="w-4 h-4 text-green-500" />
      ) : (
        <XCircle className="w-4 h-4 text-red-500" />
      )}
      <span className="text-sm">{label}</span>
      <Badge variant={status ? "default" : "secondary"}>
        {status ? "Ready" : "Not Configured"}
      </Badge>
    </div>
  )

  return (
    <div className="flex flex-col h-full bg-white border-r">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-500" />
            <h2 className="font-semibold">AI Tools</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            ×
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
          <TabsTrigger value="generate" className="flex items-center gap-1">
            <Wand2 className="w-4 h-4" />
            Generate
          </TabsTrigger>
          <TabsTrigger value="edit" className="flex items-center gap-1">
            <ImageIcon className="w-4 h-4" />
            Edit
          </TabsTrigger>
          <TabsTrigger value="enhance" className="flex items-center gap-1">
            <Sparkles className="w-4 h-4" />
            Enhance
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-1">
            <Settings className="w-4 h-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <ScrollArea className="flex-1">
          <TabsContent value="generate" className="m-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Wand2 className="w-5 h-5" />
                  Generate Image
                </CardTitle>
                <CardDescription>
                  Create images from text descriptions using AI models
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="model-select">AI Model</Label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select AI model" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels
                        .filter(model => model.category === 'text-to-image')
                        .sort((a, b) => {
                          // Sort default model first, then by provider priority
                          if (a.default && !b.default) return -1
                          if (!a.default && b.default) return 1
                          const providerOrder = { together: 0, fal: 1, clipdrop: 2, replicate: 3 }
                          return (providerOrder[a.provider] || 999) - (providerOrder[b.provider] || 999)
                        })
                        .map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex items-center gap-2">
                            {model.provider === 'together' && <Zap className="w-4 h-4" />}
                            {model.provider === 'fal' && <Sparkles className="w-4 h-4" />}
                            {model.provider === 'replicate' && <RefreshCw className="w-4 h-4" />}
                            <span>{model.name}</span>
                            {model.default && <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">Default</span>}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="prompt">Prompt</Label>
                  <Textarea
                    id="prompt"
                    placeholder="A beautiful sunset over mountains, highly detailed, photorealistic..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    rows={4}
                    disabled={isGenerating}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Width</Label>
                    <Select
                      value={generationOptions.width.toString()}
                      onValueChange={(value) => setGenerationOptions(prev => ({
                        ...prev,
                        width: parseInt(value)
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="512">512px</SelectItem>
                        <SelectItem value="768">768px</SelectItem>
                        <SelectItem value="1024">1024px</SelectItem>
                        <SelectItem value="1536">1536px</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Height</Label>
                    <Select
                      value={generationOptions.height.toString()}
                      onValueChange={(value) => setGenerationOptions(prev => ({
                        ...prev,
                        height: parseInt(value)
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="512">512px</SelectItem>
                        <SelectItem value="768">768px</SelectItem>
                        <SelectItem value="1024">1024px</SelectItem>
                        <SelectItem value="1536">1536px</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Steps: {generationOptions.steps}</Label>
                  <Slider
                    value={[generationOptions.steps]}
                    onValueChange={([value]) => setGenerationOptions(prev => ({
                      ...prev,
                      steps: value
                    }))}
                    min={10}
                    max={50}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Guidance: {generationOptions.guidance}</Label>
                  <Slider
                    value={[generationOptions.guidance]}
                    onValueChange={([value]) => setGenerationOptions(prev => ({
                      ...prev,
                      guidance: value
                    }))}
                    min={1}
                    max={20}
                    step={0.5}
                  />
                </div>

                <Button
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isGenerating || !apiStatus.anyConfigured}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4 mr-2" />
                      Generate Image
                    </>
                  )}
                </Button>

                {!apiStatus.anyConfigured && (
                  <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <AlertCircle className="w-4 h-4 text-yellow-600" />
                    <span className="text-sm text-yellow-800">
                      No AI providers configured. Check settings tab.
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="edit" className="m-0 p-4 space-y-4">
            {selectedObject && selectedObject.type === 'image' ? (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <RefreshCw className="w-5 h-5" />
                      Generate Variations
                    </CardTitle>
                    <CardDescription>
                      Create variations of the selected image with custom prompts
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="variation-prompt">Variation Prompt</Label>
                      <Textarea
                        id="variation-prompt"
                        placeholder="Describe how you want to modify this image..."
                        value={variationPrompt}
                        onChange={(e) => setVariationPrompt(e.target.value)}
                        rows={3}
                        disabled={isProcessing}
                      />
                    </div>

                    <Button
                      onClick={handleGenerateVariations}
                      disabled={!variationPrompt.trim() || generateVariationsMutation.isPending}
                      className="w-full"
                    >
                      {generateVariationsMutation.isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Generating Variations...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2" />
                          Generate Variations
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Palette className="w-5 h-5" />
                      AI Style Filters
                    </CardTitle>
                    <CardDescription>
                      Apply artistic styles to your image
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      {styleFilters.map((filter) => (
                        <Button
                          key={filter.id}
                          variant="outline"
                          onClick={() => handleApplyStyleFilter(filter.id)}
                          disabled={applyStyleFilterMutation.isPending}
                          className="h-auto p-3 flex flex-col items-center gap-2"
                        >
                          <span className="text-2xl">{filter.icon}</span>
                          <span className="text-sm font-medium">{filter.name}</span>
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center gap-4 text-center">
                    <ImageIcon className="w-12 h-12 text-gray-400" />
                    <div>
                      <h3 className="font-medium text-gray-900">No Image Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Select an image on the canvas to edit and apply variations or style filters
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="enhance" className="m-0 p-4 space-y-4">
            {selectedObject && selectedObject.type === 'image' ? (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Scissors className="w-5 h-5" />
                      Background Removal
                    </CardTitle>
                    <CardDescription>
                      Automatically remove the background from your image
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Processing Provider</Label>
                      <Select
                        value={upscaleOptions.provider}
                        onValueChange={(value) => setUpscaleOptions(prev => ({
                          ...prev,
                          provider: value
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="clipdrop" disabled={!apiStatus.clipdrop}>
                            ClipDrop (Recommended) {!apiStatus.clipdrop && '(Not configured)'}
                          </SelectItem>
                          <SelectItem value="fal" disabled={!apiStatus.fal}>
                            Fal.ai BiRefNet {!apiStatus.fal && '(Not configured)'}
                          </SelectItem>
                          <SelectItem value="replicate" disabled={!apiStatus.replicate}>
                            Replicate {!apiStatus.replicate && '(Not configured)'}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Button
                      onClick={handleRemoveBackground}
                      disabled={
                        removeBgMutation.isPending ||
                        isProcessing ||
                        !selectedObject ||
                        selectedObject.type !== 'image'
                      }
                      className="w-full"
                    >
                      {(removeBgMutation.isPending || isProcessing) ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Removing Background...
                        </>
                      ) : (
                        <>
                          <Scissors className="w-4 h-4 mr-2" />
                          Remove Background
                        </>
                      )}
                    </Button>

                    {(!selectedObject || selectedObject.type !== 'image') && (
                      <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <Info className="w-4 h-4 text-blue-600" />
                        <span className="text-sm text-blue-800">
                          Select an image on the canvas to remove its background
                        </span>
                      </div>
                    )}

                    {!apiStatus[upscaleOptions.provider] && (
                      <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <AlertCircle className="w-4 h-4 text-yellow-600" />
                        <span className="text-sm text-yellow-800">
                          {upscaleOptions.provider} provider not configured. Check settings tab.
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <ArrowUp className="w-5 h-5" />
                      Image Upscaling
                    </CardTitle>
                    <CardDescription>
                      Increase image resolution with AI enhancement
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Scale Factor</Label>
                        <Select
                          value={upscaleOptions.scale.toString()}
                          onValueChange={(value) => setUpscaleOptions(prev => ({
                            ...prev,
                            scale: parseInt(value)
                          }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="2">2x (Double)</SelectItem>
                            <SelectItem value="4">4x (Quadruple)</SelectItem>
                            <SelectItem value="8">8x (Maximum)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>Provider</Label>
                        <Select
                          value={upscaleOptions.provider}
                          onValueChange={(value) => setUpscaleOptions(prev => ({
                            ...prev,
                            provider: value
                          }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="fal" disabled={!apiStatus.fal}>
                              Fal.ai Clarity {!apiStatus.fal && '(Not configured)'}
                            </SelectItem>
                            <SelectItem value="replicate" disabled={!apiStatus.replicate}>
                              Real-ESRGAN {!apiStatus.replicate && '(Not configured)'}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <Button
                      onClick={handleUpscaleImage}
                      disabled={upscaleImageMutation.isPending || !apiStatus[upscaleOptions.provider]}
                      className="w-full"
                    >
                      {upscaleImageMutation.isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Upscaling Image...
                        </>
                      ) : (
                        <>
                          <ArrowUp className="w-4 h-4 mr-2" />
                          Upscale Image ({upscaleOptions.scale}x)
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center gap-4 text-center">
                    <Sparkles className="w-12 h-12 text-gray-400" />
                    <div>
                      <h3 className="font-medium text-gray-900">No Image Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        Select an image on the canvas to enhance with background removal or upscaling
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="settings" className="m-0 p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  AI Provider Configuration
                </CardTitle>
                <CardDescription>
                  Manage your AI service providers and check their status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    Provider Status
                  </h4>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Zap className="w-5 h-5 text-blue-500" />
                        <div>
                          <div className="font-medium">Together AI</div>
                          <div className="text-sm text-gray-500">Primary provider - FLUX Dev (Default)</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {apiStatus.together ? (
                          <>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <Badge variant="default">Ready</Badge>
                          </>
                        ) : (
                          <>
                            <XCircle className="w-4 h-4 text-red-500" />
                            <Badge variant="secondary">Not Configured</Badge>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Sparkles className="w-5 h-5 text-purple-500" />
                        <div>
                          <div className="font-medium">Fal.ai</div>
                          <div className="text-sm text-gray-500">High-quality models & processing</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {apiStatus.fal ? (
                          <>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <Badge variant="default">Ready</Badge>
                          </>
                        ) : (
                          <>
                            <XCircle className="w-4 h-4 text-red-500" />
                            <Badge variant="secondary">Not Configured</Badge>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <RefreshCw className="w-5 h-5 text-green-500" />
                        <div>
                          <div className="font-medium">Replicate</div>
                          <div className="text-sm text-gray-500">Legacy support & fallback</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {apiStatus.replicate ? (
                          <>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <Badge variant="default">Ready</Badge>
                          </>
                        ) : (
                          <>
                            <XCircle className="w-4 h-4 text-red-500" />
                            <Badge variant="secondary">Not Configured</Badge>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Scissors className="w-5 h-5 text-orange-500" />
                        <div>
                          <div className="font-medium">ClipDrop</div>
                          <div className="text-sm text-gray-500">Professional background removal (Default)</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {apiStatus.clipdrop ? (
                          <>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <Badge variant="default">Ready</Badge>
                          </>
                        ) : (
                          <>
                            <XCircle className="w-4 h-4 text-red-500" />
                            <Badge variant="secondary">Not Configured</Badge>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <Eye className="w-4 h-4" />
                    Available Models
                  </h4>
                  <div className="text-sm text-gray-600">
                    {availableModels.length > 0 ? (
                      <div className="space-y-2">
                        <div>✅ {availableModels.filter(m => m.category === 'text-to-image').length} text-to-image models</div>
                        <div>✅ {availableModels.filter(m => m.category === 'image-processing').length} image processing models</div>
                        <div>✅ {styleFilters.length} style filters available</div>
                      </div>
                    ) : (
                      <div className="text-yellow-600">⚠️ No models available - configure API keys</div>
                    )}
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Setup Instructions</h4>
                  <div className="text-sm space-y-2">
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="font-medium text-blue-900">1. Get API Keys</div>
                      <div className="text-blue-700 mt-1">
                        Sign up at Together.ai, Fal.ai, ClipDrop.co, or Replicate.com to get your API keys
                      </div>
                    </div>
                    <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="font-medium text-green-900">2. Configure Environment</div>
                      <div className="text-green-700 mt-1">
                        Add your API keys to the .env.local file in your project root
                      </div>
                    </div>
                    <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
                      <div className="font-medium text-purple-900">3. Restart Server</div>
                      <div className="text-purple-700 mt-1">
                        Restart your development server to load the new environment variables
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </ScrollArea>
      </Tabs>
    </div>
  )
}
